const modules = [
  'react-native',
  'react-native-restart',
  '@react-native',
  '@codler',
  '(jest-)?react-native...',
  'react-native-iphone-x-helper',
  'react-native-modal-datetime-picker',
  '@react-native-community/datetimepicker',
  'react-native-image-crop-picker',
  'react-native-picker',
  '@react-native-picker',
  '@react-native-picker/picker',
  'react-native-dialog',
  'react-native-element-dropdown',
  'react-native-swipe-list-view',
  'native-base',
  'native-base-shoutem-theme',
  'react-native-easy-grid',
  'react-native-super-grid',
  'react-native-drawer',
  'react-native-vector-icons',
  './icons/Nucleo',
  'react-native-push-notification',
  '@react-native-community/push-notification-ios',
  'react-native-raw-bottom-sheet',
  'react-native-picker',
  'react-native-image-zoom-viewer',
  'react-native-image-pan-zoom',
  '@react-native-camera-roll',
  'react-native-meteor',
  '@uppy/core',
  '@uppy/tus',
  '@uppy/transloadit',
  'react-native-tag-input',
  'react-redux',
  'react-native-htmlview',
  '@fortawesome/react-native-fontawesome',
  '@react-native-async-storage',
  'react-native-gesture-handler'

];
const transformIgnorePatterns = [`node_modules/(?!(${modules.join('|')})/)`];

module.exports = {
  preset: 'react-native',
  collectCoverageFrom: [
    '**/app/**',
    '!**/node_modules/**',
    '!**/e2e/**',
    '!**/images/**',
    '!**/config/**',
    '!**/android/**',
    '!**/ios/**',
    '!**/.bundle/**',
  ],
  setupFilesAfterEnv: ['./jest.setup.js', '@testing-library/jest-native/extend-expect'],
  modulePathIgnorePatterns: ['/e2e/'],
  transformIgnorePatterns,
};

