{"name": "ly.tend.tendly.tendlyapp", "version": "0.0.1", "private": true, "scripts": {"preinstall": "npx npm-force-resolutions", "start": "npx expo start", "start:legacy": "node node_modules/react-native/local-cli/cli.js start --reset-cache", "ios": "expo run:ios", "android": "expo run:android", "test": "jest --silent --coverage --json --outputFile=test-results.json --coverageReporters='json-summary'", "watch": "jest --watch --silent", "lint": "eslint .", "postinstall": "patch-package && node pickerUpdate.js", "e2e:build-ios-debug": "detox build --c ios.sim.debug", "e2e:build-android-debug": "detox build --c android.emu.debug", "e2e:test-ios-debug": "detox test --c ios.sim.debug", "e2e:test-android-debug": "detox test --c android.emu.debug", "e2e:build-ios": "detox build --c ios.sim.release", "e2e:build-android": "detox build --c android.emu.release", "e2e:test-ios": "detox test --c ios.sim.release", "e2e:test-android": "detox test --c android.emu.release", "e2e:test-android-att": "detox test --c android.att.debug", "seed:invoices": "node scripts/createInvoices.js", "eas-build-on-success": "npx bugsnag-eas-build-on-success"}, "dependencies": {"@bugsnag/expo": "^50.0.0", "@bugsnag/react-native": "^7.18.2", "@bugsnag/react-native-performance": "^2.10.1", "@codler/react-native-keyboard-aware-scroll-view": "2.0.0", "@expo/vector-icons": "^13.0.0", "@fortawesome/fontawesome-pro": "^6.4.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/pro-light-svg-icons": "^6.4.0", "@fortawesome/react-native-fontawesome": "^0.3.2", "@intercom/intercom-react-native": "^4.0.1", "@meteorrn/core": "^2.8.1", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-camera-roll/camera-roll": "^7.2.0", "@react-native-community/datetimepicker": "7.7.0", "@react-native-community/masked-view": "^0.1.0", "@react-native-community/netinfo": "11.1.0", "@react-native-community/progress-bar-android": "^1.0.5", "@react-native-community/progress-view": "^1.3.1", "@react-native-community/push-notification-ios": "1.8.0", "@react-native-cookies/cookies": "^6.2.1", "@react-native-picker/picker": "2.6.1", "@react-native/metro-config": "^0.73.2", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^6.4.1", "@uppy/core": "1.16.2", "@uppy/transloadit": "1.6.22", "appcenter-cli": "^1.1.18", "aws-sdk": "^2.1692.0", "config-plugin-react-native-intercom": "^1.10.1", "csv-writer": "^1.6.0", "deprecated-react-native-prop-types": "^5.0.0", "dotenv": "^16.4.7", "expo": "^50.0.0", "expo-application": "~5.8.4", "expo-build-properties": "~0.11.1", "expo-constants": "~15.4.6", "expo-crypto": "~12.8.1", "expo-dev-client": "~3.3.12", "expo-device": "~5.9.4", "expo-file-system": "~16.0.9", "expo-font": "~11.10.3", "expo-insights": "~0.6.1", "expo-notifications": "~0.27.8", "expo-secure-store": "~12.8.1", "expo-updates": "^0.24.13", "fbjs": "^3.0.5", "install": "^0.13.0", "lodash": "^4.17.11", "moment": "^2.23.0", "moment-timezone": "^0.5.23", "mongodb": "^6.13.1", "nanoid": "^5.0.3", "native-base": "2.15.2", "npm": "^6.14.12", "numeral": "^2.0.6", "patch-package": "^8.0.0", "react": "18.2.0", "react-error-boundary": "^4.0.11", "react-lifecycles-compat": "^3.0.4", "react-native": "0.73.6", "react-native-animatable": "^1.3.1", "react-native-calendar-strip": "^1.3.8", "react-native-camera": "^4.2.1", "react-native-device-info": "^8.3.1", "react-native-dialog": "^9.3.0", "react-native-element-dropdown": "^2.8.0", "react-native-fast-image": "8.3.4", "react-native-file-access": "^3.1.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.14.0", "react-native-get-random-values": "~1.8.0", "react-native-htmlview": "^0.13.0", "react-native-hyperlink": "0.0.18", "react-native-image-crop-picker": "^0.41.2", "react-native-image-zoom-viewer": "3.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-launch-arguments": "^4.0.2", "react-native-linear-gradient": "^2.6.2", "react-native-meteor": "file:./libs/react-native-meteor", "react-native-modal-datetime-picker": "^17.1.0", "react-native-neat-date-picker": "^1.4.9", "react-native-orientation-locker": "1.1.8", "react-native-pdf": "6.2.1", "react-native-permissions": "^3.10.0", "react-native-picker": "github:macoffma/react-native-picker", "react-native-progress-bar-animated": "^1.0.6", "react-native-push-notification": "^8.1.1", "react-native-qrcode-scanner": "^1.5.4", "react-native-qrcode-svg": "^6.2.0", "react-native-raw-bottom-sheet": "^2.2.0", "react-native-restart": "0.0.13", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-share": "^7.9.1", "react-native-super-grid": "4.1.1", "react-native-svg": "14.1.0", "react-native-swipe-list-view": "3.2.6", "react-native-tab-view": "^3.1.1", "react-native-tag-input": "^0.0.21", "react-native-vector-icons": "^8.1.0", "react-native-video": "^5.2.1", "react-native-video-controls": "2.8.1", "react-native-webview": "13.6.4", "react-redux": "^9.1.2", "react-timeout": "2.0.1", "react-timer-mixin": "^0.13.4", "redux": "^5.0.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^2.3.0", "rn-fetch-blob": "0.12.0", "shortid": "^2.2.16", "sms-segments-calculator": "^1.1.1", "text-encoding": "^0.7.0", "underscore": "^1.9.1", "unstated": "^2.1.1", "uuid": "^9.0.1"}, "devDependencies": {"@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@bugsnag/plugin-expo-eas-sourcemaps": "^50.0.0", "@bugsnag/source-maps": "^2.3.3", "@react-native/babel-preset": "^0.73.18", "@react-native/eslint-config": "^0.73.1", "@react-native/gradle-plugin": "^0.73.4", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.2.2", "@types/react": "~18.2.45", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^26.6.3", "detox": "^20.36.3", "eslint": "^9.23.0", "eslint-plugin-prettier": "^5.2.5", "jest": "29.6.4", "jest-config": "29.6.4", "jest-environment-jsdom": "29.6.4", "jest-fetch-mock": "^3.0.3", "metro-react-native-babel-preset": "^0.66.2", "prettier": "^3.5.3", "react": "18.2.0", "react-native-dotenv": "^3.4.11", "react-test-renderer": "18.2.0", "redux-mock-store": "^1.5.4", "typescript": "^5.3.0"}, "engines": {"node": ">=18"}, "resolutions": {"@codler/react-native-keyboard-aware-scroll-view": "2.0.1"}, "overrides": {"react-native-qrcode-scanner": {"react-native-permissions": "^3.10.0"}, "native-base": {"@codler/react-native-keyboard-aware-scroll-view": "2.0.1"}}, "expo": {"autolinking": {"exclude": ["expo-keep-awake"]}}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}