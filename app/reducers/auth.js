import {
  AUTH_RECEIVE_USER_TOKEN,
  AUTH_RECEIVE_USERDATA,
  AUTH_CLEAR_DATA,
  AUTH_REHYDRATE_DATA,
  AUTH_CLEAR_BADGE,
  AUTH_TOGGLE_OFFLINEMODE,
  AUTH_TOGGLE_ONLINEMODE,
  AUTH_RECEIVE_STAFFADMINPEOPLE,
  AUTH_SET_PEOPLE_SCOPE,
  AUTH_USER_DATA_CALL,
  AUTH_TRACK_BACKGROUND,
  AUTH_C<PERSON>AR_BACKGROUND,
  AUTH_PROCESS_BACKGROUND,
  AUTH_SET_PIN_PROMPT,
  AUTH_SET_SWITCHABLE_ORGS,
  AUTH_SET_SWITCHABLE_MEMBERSHIPS,
  AUTH_SET_LOADING,
  AUTH_SET_ACTIVE_ACCOUNTS,
  SET_UNREAD_MESSAGES,
  OFFLINE_UPDATE_PEOPLE
} from '../utility/actions';

import Meteor from 'react-native-meteor';
import _ from 'lodash';
import moment from 'moment';
import * as environmentSettings from '../shared/Settings';
import constants from '../config/constants.json'

const initialState = {
  resumeToken: null,
  userInfo: null,
  personInfo: null,
  loading: false,
  orgInfo: null,
  appHelpInfo: [],
  appHelpBadgeDate: null,
  appHelpLastChecked: null,
  subscribeToRender: null,
  lastUserDataCall: new Date().valueOf(),
  backgroundAppEntry: null,
  showPinCodePrompt: false,
  dashboard: {},
  allPeople: [],
  allGroups: [],
  allFood: [],
  allCurriculums: [],
  allRelationships: [],
  directory: [],
  momentDefInfo: [],
  offlineModeEnabled: false,
  offlineMode: false,
  offlinePeople: [],
  offlineGroups: [],
  peopleScope: null,
  switchableOrgs: [],
  switchableMemberships: [],
  activeAccounts: [],
  hasUnreadMessages: false,
};

import { createReducer, updateState } from '../utility/reducers';

export default createReducer(initialState, {
  [AUTH_CLEAR_BACKGROUND]: (state, action) => {
    return { ...state, backgroundAppEntry: null, showPinCodePrompt: false };
  },
  [AUTH_TRACK_BACKGROUND]: (state, action) => {
    if (state.backgroundAppEntry == null) {
      return { ...state, backgroundAppEntry: new Date().valueOf() };
    }

    return {...state}
  },
  [AUTH_PROCESS_BACKGROUND]: (state, action) => {
    const { backgroundAppEntry } = state;
    let showPinCodePrompt = false;
    const now = new moment();
    // console.log("bg app entry is ", backgroundAppEntry)
    // console.log("diff is ", now.diff(new moment(backgroundAppEntry)))
    if (environmentSettings.PIN_CODE_PROMPT_DELAY == false) {
      return { ...state }
    }

    if (backgroundAppEntry && now.diff(new moment(backgroundAppEntry)) >= environmentSettings.PIN_CODE_PROMPT_DELAY) {
      showPinCodePrompt = true;
    }

    return { ...state, showPinCodePrompt };
  },
  [AUTH_SET_PIN_PROMPT]: (state, action) => {
    return { ...state, backgroundAppEntry: null, showPinCodePrompt: true };
  },
  [AUTH_RECEIVE_USERDATA]: (state, action) => {
    const {
      userInfo,
      personInfo,
      orgInfo,
      momentDefInfo,
      appHelpInfo,
      appHelpBadgeDate,
      allGroups,
      allPeople,
      allFood,
      allCurriculums,
      allRelationships,
      directory,
      dashboard,
    } = action?.payload;

    if (_.isArray(momentDefInfo)) {
      _.each(momentDefInfo, (md) => {
        Meteor.collection('momentDefinitions').saveLocal(md);
      });
    }
    _.each(allGroups, (g) => {
      Meteor.collection('groups').saveLocal(g);
    });

    _.each(allPeople, (p) => {
      Meteor.collection('people').saveLocal(p);
    });

    _.each(allFood, (f) => {
      Meteor.collection('food').saveLocal(f);
    });

    _.each(allCurriculums, (c) => {
      Meteor.collection('curriculum').saveLocal(c);
    });

    _.each(allRelationships, (r) => {
      Meteor.collection('relationships').saveLocal(r);
    });

    _.each(directory, (d) => {
      Meteor.collection('directory').saveLocal(d);
    });

    console.log("SAVING LOCAL DATA IN USERS")
    Meteor.collection('users').saveLocal(userInfo);
    Meteor.collection('people').saveLocal(personInfo);
    Meteor.collection('orgs').saveLocal(orgInfo);
    const offlineModeEnabled = orgInfo?.customizations?.["mobile/offlineMode/enabled"] ?? false;
    const offlinePeople = allPeople
    return updateState(state, {
      userInfo,
      personInfo,
      orgInfo,
      appHelpInfo,
      appHelpBadgeDate,
      offlineModeEnabled,
      momentDefInfo,
      allPeople,
      allGroups,
      allFood,
      allCurriculums,
      allRelationships,
      directory,
      dashboard,
      offlinePeople,
      loading: false,
      subscribeToRender: new Date().valueOf(),
    });
  },
  [AUTH_USER_DATA_CALL]: (state, action) => {
    const { lastUserDataCall } = action.payload;
    return updateState(state, { lastUserDataCall });
  },
  [AUTH_RECEIVE_USER_TOKEN]: (state, action) => {
    const { resumeToken, lastUserDataCall } = action.payload;
    return updateState(state, { resumeToken, lastUserDataCall });
  },
  [AUTH_SET_PEOPLE_SCOPE]: (state, action) => {
    const { scope } = action.payload;
    return updateState(state, {peopleScope: scope});
  },
  [AUTH_RECEIVE_STAFFADMINPEOPLE]: (state, action) => {
    const {allPeople, dashboard, allGroups} = action.payload;
    // ignore any data update if orgInfo is null; most likely need to reauth user
    if (state.orgInfo != null) {
      _.each(allGroups, (g) => {
          Meteor.collection('groups').saveLocal(g);
      });
      _.each(allPeople, (p) => {
        let existing = Meteor.collection('people').findOne(p._id);
        if (existing) {
          delete existing.familyCheckIn;
          delete existing.familyCheckOut;
          const existingPD = existing?.profileData ?? {};
          const newPD = p?.profileData ?? {};
          const profileDataMerge = { ...existingPD, ...newPD };
          const newPerson = {...existing, ...p, profileData: profileDataMerge};
          Meteor.collection('people').saveLocal(newPerson);
        } else {
          Meteor.collection('people').saveLocal(p);
        }
      });
      // console.log("AUTH_RECEIVE_STAFFADMINPEOPLE")
      return { ...state, dashboard, subscribeToRender: new Date().valueOf() };
    }
    return { ...state };
  },
  [AUTH_REHYDRATE_DATA]: (state, action) => {
    const { userInfo, personInfo, allGroups, allPeople, allFood, allCurriculums, allRelationships, orgInfo, directory, momentDefInfo } = state;

    if (_.isArray(momentDefInfo)) {
      _.each(momentDefInfo, (md) => {
        Meteor.collection('momentDefinitions').saveLocal(md);
      });
    }

    _.each(allGroups, (g) => {
      Meteor.collection('groups').saveLocal(g);
    });

    _.each(allPeople, (p) => {
      Meteor.collection('people').saveLocal(p);
    });

    _.each(allFood, (f) => {
      Meteor.collection('food').saveLocal(f);
    });

    _.each(allCurriculums, (c) => {
      Meteor.collection('curriculum').saveLocal(c);
    });

    _.each(allRelationships, (r) => {
      Meteor.collection('relationships').saveLocal(r);
    });

    _.each(directory, (d) => {
      Meteor.collection('directory').saveLocal(d);
    });

    Meteor.collection('users').saveLocal(userInfo);
    Meteor.collection('people').saveLocal(personInfo);
    Meteor.collection('orgs').saveLocal(orgInfo);
    // console.log("AUTH_REHYDRATE_DATA")
    return { ...state, subscribeToRender: new Date().valueOf() };
  },
  [AUTH_CLEAR_DATA]: (state, action) => {
    const { loading } = action.payload;
    environmentSettings.setBaseServer(constants.BASE_SERVER);
    return updateState(state, {...initialState, loading, appHelpLastChecked: new moment() });
  },
  [AUTH_CLEAR_BADGE]: (state, action) => {
    return updateState(state, { appHelpLastChecked: new moment() })
  },
  [AUTH_TOGGLE_OFFLINEMODE]: (state, action) => {
    const allPeople = Meteor.collection('people').find({ type: {$in: ["staff", "admin", "person"]}});
    const allGroups = Meteor.collection('groups').find();
    Meteor.disconnect();
    const stateUpdate = {
      offlineMode: true
    };
    if (allPeople?.length > 1) stateUpdate.offlinePeople = allPeople;
    if (allGroups?.length > 0) stateUpdate.offlineGroups = allGroups;

    return updateState(state, stateUpdate);
  },
  [AUTH_TOGGLE_ONLINEMODE]: (state, action) => {
    let serverUrl = environmentSettings.WEB_SOCKET_URL;
    console.log("Connecting to: " + serverUrl);
    Meteor.connect(serverUrl);

    return updateState(state, { offlineMode: false });
  },
  [AUTH_SET_SWITCHABLE_ORGS]: (state, action) => {
    const { switchableOrgs } = action.payload;
    return updateState(state, { switchableOrgs });
  },
  [AUTH_SET_SWITCHABLE_MEMBERSHIPS]: (state, action) => {
    const { switchableMemberships, memberships } = action.payload;
    return updateState(state, { switchableMemberships, memberships });
  },
  [AUTH_SET_ACTIVE_ACCOUNTS]: (state, action) => {
    const { activeAccounts } = action.payload;
    return updateState(state, { activeAccounts });
  },
  [AUTH_SET_LOADING]: (state, action) => {
    const { loading } = action.payload;
    return updateState(state, { loading });
  },
  [SET_UNREAD_MESSAGES]: (state, action) => {
    const { hasUnread } = action.payload;
    return updateState(state, { hasUnread });
  },
  [OFFLINE_UPDATE_PEOPLE]: (state, action) => {
    // Get fresh list of people from the Meteor collection
    const freshPeople = Meteor.collection('people').find({ type: {$in: ["staff", "admin", "person"]}});
    return updateState(state, { offlinePeople: freshPeople });
  }
})
