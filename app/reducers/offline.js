import {
  OFFLINE_ADD_SLEEP_MOMENTS,
  OFFLINE_SLEEP_CHECK,
  OFFLINE_END_SLEEP,
  OFFLINE_NEW_MOMENT,
  OFFLINE_SYNC_MOMENTS,
  OFFLINE_START_SYNC_MOMENTS,
  OFFLINE_END_SYNC_MOMENTS,
  OFFLINE_CLEAR_MOMENTS,
} from '../utility/actions';

import Meteor from 'react-native-meteor';
import _ from 'lodash';
import moment from 'moment';

const initialState = {
  sleepMoments: [],
  momentsToSync: [],
  momentsSyncing: false,
};

import { createReducer, updateState } from '../utility/reducers';

export default createReducer(initialState, {
  [OFFLINE_ADD_SLEEP_MOMENTS]: (state, action) => {
    const { newMoments } = action.payload;
    const newSleepMoments = state.sleepMoments.concat(newMoments);
    return updateState(state, { sleepMoments: newSleepMoments });
  },
  [OFFLINE_SLEEP_CHECK]: (state, action) => {
    const { sleepMoment } = action.payload;
    const existingSleepMoments = state.sleepMoments.filter((savedSleepMoment) => savedSleepMoment._id !== sleepMoment._id);
    existingSleepMoments.push(sleepMoment);
    return updateState(state, {sleepMoments: existingSleepMoments});
  },
  [OFFLINE_END_SLEEP]: (state, action) => {
    const { sleepMoment } = action.payload;
    const remainingSleepMoments = _.filter(state.sleepMoments, (s) => s._id != sleepMoment._id);
    const momentsToSync = [sleepMoment].concat(state.momentsToSync);

    return updateState(state, { sleepMoments: remainingSleepMoments, momentsToSync });
  },
  [OFFLINE_NEW_MOMENT]: (state, action) => {
    // Handle both single moment and multiple moments
    if (action.payload.newMoment) {
      // Single moment case
      const { newMoment } = action.payload;
      const momentsToSync = [newMoment].concat(state.momentsToSync);
      return updateState(state, { momentsToSync });
    } else if (action.payload.newMoments) {
      // Multiple moments case (used when loading from AsyncStorage)
      const { newMoments } = action.payload;
      return updateState(state, { momentsToSync: newMoments });
    }
    return state;
  },
  [OFFLINE_SYNC_MOMENTS]: (state) => {
    return updateState(state, {momentsToSync: [], momentsSyncing: false});
  },
  [OFFLINE_START_SYNC_MOMENTS]: (state, action) => {
    return updateState(state, {momentsSyncing: action.payload.momentsSyncing});
  },
  [OFFLINE_END_SYNC_MOMENTS]: (state, action) => {
    return updateState(state, {momentsSyncing: action.payload.momentsSyncing});
  },
  [OFFLINE_CLEAR_MOMENTS]: (state) => {
    return updateState(state, {momentsToSync: []});
  },
})
