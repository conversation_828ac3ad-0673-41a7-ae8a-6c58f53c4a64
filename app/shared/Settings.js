import constants from '../config/constants.json';
import { Platform } from 'react-native';

export let BASE_SERVER = constants.BASE_SERVER

export let APP_URL = "https://"  + BASE_SERVER;

export let WEB_SOCKET_URL = "wss://" + BASE_SERVER + "/websocket"

export const setBaseServer = (url) => {
  BASE_SERVER = url;
  APP_URL = "https://" + BASE_SERVER;
  WEB_SOCKET_URL = "wss://" + BASE_SERVER + "/websocket";
}

export const PHOTO_BASE_URL = constants.PHOTO_BASE_URL || 'https://cfm.momentpath.com/';

export const DEFAULT_PRIMARY_COLOR = "#c199d4";

export const SAML_URL = constants.SAML_URL || "https://app.momentpath.com/_saml/authorize/";

export const PW_APPID = constants.PW_APPID || "4C033-114B3";
export const PW_PROJECT_NUMBER = constants.PW_PROJECT_NUMBER || "925686313898";
export const PEEK_OF_THE_WEEK_IMGAE = constants.PEEK_OF_THE_WEEK_IMGAE;
export const ACTIVITY_LIST_STANDARD_CARDS = constants.ACTIVITY_LIST_STANDARD_CARDS;
export const PUSH_APPLICATION_ARN = (Platform.OS == 'ios') ? constants.IOS_PUSH_APPLICATION_ARN : constants.ANDROID_PUSH_APPLICATION_ARN;
export const WMG_CARD_TITLE = constants.WMG_CARD_TITLE || "ParentView® powered by WatchMeGrow";
export const WMG_PLAYER_URL = constants.WMG_PLAYER_URL || "https://presence.internal.watchmegrow.com/v1/embed/player";
export const SUPPORT_SITE = constants.SUPPORT_SITE || "https://help-beta.momentpath.com";
export const EDU_CARD_TITLE = constants.EDU_CARD_TITLE || "Express Drive-Up"
export const MOBILE_API_KEY = constants.API_KEY || "8tsD8v7q4ndpkMryhFJ9Hi8AZfKAsKr6jdz8NCZxPHvmBVw9oUXvLpZCGEy6uCdN8nk778JvTFWTUXy6MbayvucnvGLMDrbGgAZG";
export const PIN_CODE_PROMPT_DELAY = constants.PIN_CODE_PROMPT_DELAY || false;
export const WL_PIN_LOGIN = constants.WL_PIN_LOGIN || false;
export const WL_PIN_LOGIN_ORG_ID = constants.WL_PIN_LOGIN_ORG_ID || "n/a";
export const COGNITO_AUTH = constants.COGNITO_AUTH || true;

export const BUGSNAG_APPID = "33b2d76d54d686eeedcb6293b7d319ae"
