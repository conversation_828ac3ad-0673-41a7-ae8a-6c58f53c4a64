import React, {Component} from 'react';
import {
  Dimensions,
  ScrollView,
  FlatList,
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import Meteor, {withTracker} from 'react-native-meteor';
import Nucleo from './icons/Nucleo';
import colors from '../config/colors.json';
import AsyncStorage from '@react-native-async-storage/async-storage';
import _ from 'lodash';
import Bugsnag from '@bugsnag/react-native';
import {connect} from 'react-redux';
import {
  clearUserData,
  removeAccounts,
  getUserData,
  setPinPromptAuth,
  fetchMemberships
} from '../actions/userData';
import {CommonActions, StackActions} from '@react-navigation/native';
import RBSheet from 'react-native-raw-bottom-sheet';

import CookieManager from '@react-native-cookies/cookies';
import {Picker} from '@react-native-picker/picker';
import Orgs from '../api/Orgs'

const _width = Dimensions.get('window').width;
const _height = Dimensions.get('window').height;
const ACCOUNTS_KEY = 'tendlyrn_accountsv1';

const styles = {
  componentWrapper: {
    height: _height,
    width: _width,
    position: 'absolute',
    top: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
    elevation: 1,
  },
  touchableWrapper: {
    width: _width,
    height: _height,
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 2,
    elevation: 2,
  },
  scrollViewWrapper: {
    maxHeight: _height - 250,
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 3,
    elevation: 3,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 16},
    shadowOpacity: 0.8,
    shadowRadius: 16,
  },
  scrollViewContainer: {
    backgroundColor: colors.white,
    borderRadius: 2,
    zIndex: 5,
    elevation: 5,
  },
  buttonView: {
    marginHorizontal: 10,
    marginVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonText: {
    color: colors.charcoalLighter,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  headerText: {
    color: colors.grayPlatform,
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  spacerView: {
    flex: 1,
    borderBottomColor: colors.lightGray,
    borderBottomWidth: 2,
    marginHorizontal: 10,
    marginBottom: 10,
    paddingBottom: 8,
  },
};

class AccountSwitcher extends Component {
  constructor(props) {
    super(props);
    this.state = {
      accounts: [],
      mounting: true,
      orgs: [],
      selectedOrg: {},
    };
  }
  componentDidMount() {
    const user = Meteor.user();
    const {dispatch} = this.props;
    if (user) {
      try {
        dispatch(fetchMemberships());
      } catch (error) {
        console.log('error fetching memberships', error);
      }
    }
  }

  componentDidUpdate(prevProps) {
    const { dispatch } = this.props;
    const userChanged = prevProps.user?._id !== this.props.user?._id;

    if (userChanged || prevProps.isVisible !== this.props.isVisible) {
      try {
        dispatch(fetchMemberships());
      } catch (error) {
        console.log('error fetching memberships', error);
      }
    }
  }

  switchUser = async user => {
    const {dispatch} = this.props;
    try {
      await AsyncStorage.removeItem('filterState');
    } catch (error) {
      console.log('error removing Async filterState: ', error);
      Bugsnag.notify(error);
    }
    try {
      CookieManager.clearAll(true)
      CookieManager.clearAll()
    } catch (error) {
      console.log('error clearing CookieManager: ', error)
      Bugsnag.notify(error);
    }
    dispatch(clearUserData(true));
    Meteor.accountSwitch(user);
    dispatch(getUserData(user.token));
    dispatch(setPinPromptAuth());
  };

  signOut = async () => {
    const {dispatch} = this.props;
    const user = Meteor.user();
    const filterString = user ? `filterState_${user._id}` : 'filterState';

    try {
      dispatch(clearUserData());
      await AsyncStorage.removeItem(filterString);
      await new Promise(resolve => Meteor.logout(resolve));
    } catch (e) {
      Bugsnag.notify(e);
    }
    CookieManager.clearAll(true).then(success => {
      console.log('CookieManager.clearAll webkit=>', success);
    });
    CookieManager.clearAll().then(success => {
      console.log('CookieManager.clearAll nonwk=>', success);
    });
  };

  addAccount = async () => {
    const {dispatch} = this.props;
    CookieManager.clearAll(true).then(success => {
      console.log('CookieManager.clearAll webkit=>', success);
    });
    CookieManager.clearAll().then(success => {
      console.log('CookieManager.clearAll nonwk=>', success);
    });
    await dispatch(clearUserData())

    Meteor.addAccount();
  };

  removeAccounts = () => {
    const user = Meteor.user();
    const {dispatch} = this.props;
    try {
      dispatch(removeAccounts(user._id))
    } catch (error) {
      console.log('error removing accounts', error);
    }
  };
  showPinCode = () => {
    this.props.navigation.navigate('ModalWebViewCaptive', {
      location: 'pinCode',
    });
  };

  profileDetails = () => {
    const currentUser = Meteor.user()
    this.props.navigation.navigate('People', {
      screen: 'Person',
      initial: false,
      params: {personId: currentUser.personId},
    });
  };

  closeSwitcher = () => {
    this.props.navigation.dispatch(
      CommonActions.setParams({showSwitcher: false, showSwitcherQRView: false}),
    );
  };
  showSiteSwitcher = () => {
    this.props.navigation.navigate('SiteSwitcher');
  };
  switchMembership = async orgId => {
    const {navigation, dispatch, activeAccounts} = this.props;
    AsyncStorage.setItem(ACCOUNTS_KEY, JSON.stringify(activeAccounts || []))
      .then(() => {
        this.closeSwitcher();
        Meteor.call('switchUserMembership', orgId, function (error, res) {
          if (error) {
            Alert.alert('Error switching membership', error.reason);
          } else {
            console.log('membership switch complete');
            Meteor.membershipSwitch();
            dispatch(getUserData(null));
            navigation.dispatch(state => {
              return CommonActions.reset({
                routes: [{name: 'Home'}, {name: 'People'}],
                index: 0,
              });
            });
          }
        });
      })
  };

  changeOrg = (itemValue) => {
    const {navigation} = this.props;
    itemValue &&
    itemValue._id &&
    navigation &&
    this.props.dispatch &&
      Meteor.call('adminSwitchOrg', itemValue._id, (error, result) => {
        navigation.setParams({showSwitcher: false});
        if (error) {
          Alert.alert('Org switching not available');
        } else {
          this.props.dispatch(getUserData());
        }
      });
  }

  render() {
    const user = Meteor.user();
    const {navigation, route, switchableMemberships, activeAccounts, fetchActiveAccounts} = this.props;
    const userPerson = route?.params?.userPerson
    const showPinCodeCheckin = route?.params?.showPinCodeCheckin ?? false;
    const currentEmail =
      user?.emails?.[0]?.address ??
      Meteor.collection('people').findOne({_id: user?._id})?.emails?.[0]
        ?.address ??
      'Loading...';
    if(!this.props.isVisible) return null;
    return (
      <View style={styles.componentWrapper}>
        <TouchableWithoutFeedback
          onPress={() => {
            requestAnimationFrame(() => {
              this.closeSwitcher();
            });
          }}>
          <View style={styles.touchableWrapper} />
        </TouchableWithoutFeedback>
        <View
          style={[
            styles.scrollViewWrapper,
            {display: this.state.showSiteSwitcher ? 'none' : 'flex'},
          ]}>
          <ScrollView
            style={styles.scrollViewContainer}
            contentContainerStyle={{
              marginVertical: 20,
              marginHorizontal: 20,
              paddingBottom: 20,
            }}>
            <Text style={styles.headerText}>Switch Accounts</Text>
            {activeAccounts?.map(acc => {
              const email = acc?.user?.emails?.[0]?.address ?? null;
              if (email && email.length > 0 && email !== currentEmail) {
                return (
                  <TouchableOpacity
                    onPress={() => this.switchUser(acc)}
                    style={styles.buttonView}>
                    <Nucleo
                      name="icon-swap-vertical"
                      size={16}
                      color={colors.charcoalLighter}
                    />
                    <Text style={styles.buttonText} numberOfLines={1}>
                      {email}
                    </Text>
                  </TouchableOpacity>
                );
              }
              return null;
            })}
            <TouchableOpacity
              onPress={this.addAccount}
              style={styles.buttonView}>
              <Nucleo
                name="icon-e-add"
                size={16}
                color={colors.charcoalLighter}
              />
              <Text style={styles.buttonText}>Add Account</Text>
            </TouchableOpacity>
            {showPinCodeCheckin && (
              <TouchableOpacity
                onPress={this.showPinCode}
                style={styles.buttonView}>
                <Nucleo
                  name="icon-apps"
                  size={16}
                  color={colors.charcoalLighter}
                />
                <Text style={styles.buttonText}>PIN Check-in</Text>
              </TouchableOpacity>
            )}
            {activeAccounts?.length >= 1 && (
              <TouchableOpacity
                onPress={this.removeAccounts}
                style={styles.buttonView}>
                <Nucleo
                  name="icon-logout"
                  size={16}
                  color={colors.charcoalLighter}
                />
                <Text style={styles.buttonText}>Remove Accounts</Text>
              </TouchableOpacity>
            )}
            <View style={styles.spacerView} />
            <Text style={[styles.headerText, {marginTop: 6}]}>
              Active Account
            </Text>
            <TouchableOpacity
              onPress={this.profileDetails}
              style={styles.buttonView}>
              <Nucleo
                name="icon-a-check"
                size={16}
                color={colors.charcoalLighter}
              />
              <Text
                testID="account-email"
                style={styles.buttonText}
                numberOfLines={1}>{`${currentEmail}`}</Text>
            </TouchableOpacity>
            {userPerson?.masterAdmin && this.props?.switchableOrgs?.length ? (
              <TouchableOpacity
                onPress={() => {
                  this.setState({showSiteSwitcher: true});
                  this.siteSwitcher.open();
                }}
                style={styles.buttonView}>
                <View
                  style={{
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: 16,
                  }}>
                  <Nucleo
                    name="icon-arrow-right"
                    size={9}
                    color={colors.charcoalLighter}
                    style={{
                      height: 10,
                    }}
                  />
                  <Nucleo
                    name="icon-arrow-left"
                    size={9}
                    color={colors.charcoalLighter}
                    style={{
                      height: 10,
                    }}
                  />
                </View>
                <Text style={styles.buttonText} numberOfLines={1}>
                  Switch Site
                </Text>
              </TouchableOpacity>
            ) : null}
            <TouchableOpacity onPress={this.signOut} style={styles.buttonView}>
              <Nucleo
                name="icon-logout"
                size={16}
                color={colors.charcoalLighter}
              />
              <Text style={styles.buttonText}>Sign Out</Text>
            </TouchableOpacity>
            {switchableMemberships?.length > 0 && (
              <View style={{flex: 1}}>
                <View style={styles.spacerView} />
                <Text style={[styles.headerText, {marginTop: 6}]}>
                  Switch Community
                </Text>
                {_.map(switchableMemberships, m => (
                  <TouchableOpacity
                    testID="community"
                    onPress={() => this.switchMembership(m._id)}
                    style={styles.buttonView}>
                    <Nucleo
                      name="icon-swap-vertical"
                      size={16}
                      color={colors.charcoalLighter}
                    />
                    <Text style={styles.buttonText} numberOfLines={1}>
                      {m.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </ScrollView>
        </View>
        <RBSheet
          ref={ref => {
            this.siteSwitcher = ref;
          }}
          onClose={() => {
            this.setState({showSiteSwitcher: false});
            navigation.setParams({showSwitcher: false});
          }}>
          <View
            style={{
              flex: 1,
              height: '100%',
            }}>
            {Platform.OS == 'ios' ? (
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  paddingLeft: 10,
                  paddingRight: 10,
                  flex: 1,
                  borderColor: colors.lightGray,
                }}>
                {this.state.selectedOrg &&
                  this.state.selectedOrg?._id != Orgs?.current()?._id && (
                    <TouchableOpacity
                      disabled={this.state.selectedOrg?._id ? false : true}
                      onPress={() => {
                        this.setState({loading: true});
                        this.changeOrg(this.state.selectedOrg);
                        this.siteSwitcher.close();
                      }}
                      style={{
                        backgroundColor: colors.primaryA,
                        alignItems: 'center',
                        fontSize: 16,
                        paddingTop: 10,
                        paddingBottom: 10,
                        borderRadius: 5,
                        marginTop: 10,
                      }}>
                      <Text
                        style={{
                          color: colors.white,
                          fontWeight: 'bold',
                          paddingLeft: 10,
                          paddingRight: 10,
                        }}>
                        Confirm
                      </Text>
                    </TouchableOpacity>
                  )}
              </View>
            ) : null}
            {Platform.OS == 'ios' ? (
              <Picker
                testID="org-switcher"
                enabled={this.props.switchableOrgs ? true : false}
                selectedValue={this.state.selectedOrg?._id || ''}
                onValueChange={(itemValue) => {
                  const selectedOrg = this.props.switchableOrgs.find(org => org._id === itemValue);
                  if (selectedOrg) {
                    this.setState({selectedOrg: selectedOrg});
                  }
                }}>
                {this.props?.switchableOrgs.map((org, i) => (
                  <Picker.Item
                    key={org._id}
                    testID={`org-${i}`}
                    label={org.name}
                    value={org._id}
                  />
                ))}
              </Picker>
            ) : (
              <FlatList
                testID="org-switcher"
                data={this.props.switchableOrgs}
                renderItem={({item}) => (
                  <TouchableOpacity
                    testID={`org-${item._id}`}
                    onPress={() => {
                      this.changeOrg(item);
                      this.siteSwitcher.close();
                    }}
                    style={{
                      borderBottomColor: colors.lightGray,
                      borderBottomWidth: 1,
                      paddingTop: 10,
                      paddingLeft: 10,
                      paddingBottom: 10,
                    }}>
                    <Text style={{fontSize: 16}}>{item.name}</Text>
                    {item?._id === this.props.user.orgId ? (
                      <Text style={{fontSize: 12, color: colors.primaryA}}>
                        Current Site
                      </Text>
                    ) : null}
                  </TouchableOpacity>
                )}
                keyExtractor={item => item?._id}
                style={{
                  marginTop: 10,
                  paddingBottom: 10,
                }}
              />
            )}
          </View>
        </RBSheet>
      </View>
    );
  }
}
const mapStateToProps = state => ({
  user: state.auth.userInfo,
  switchableOrgs: state.auth.switchableOrgs,
  switchableMemberships: state.auth.switchableMemberships,
  activeAccounts: state.auth.activeAccounts,

});

export default connect(mapStateToProps)(AccountSwitcher)
