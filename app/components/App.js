import React from 'react';
import { SafeArea<PERSON>iew, PixelRatio, Dimensions, Platform, StatusBar, StyleSheet, AppState, LogBox, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import {Root} from 'native-base';
import Meteor, { withTracker }  from 'react-native-meteor';
import HomeScreen from './HomeScreen';
import SignIn from './SignIn';
import LoadingScreen from './LoadingScreen';
import CustomErrorFallback from './CustomErrorFallback';
import { Provider } from 'unstated';
import SyncManager from '../shared/SyncManager';
import { connect } from 'react-redux';
import {getUserData, rehydrateUserData, fetchPeopleChanges, trackAppBackground, processBackgroundAuth, clearUserData, clearAppBackground} from '../actions/userData';

import People from '../api/People';

import Intercom from '@intercom/intercom-react-native';
import DeviceInfo from 'react-native-device-info';

import { enableScreens } from 'react-native-screens';

import _ from 'lodash';

import * as Updates from 'expo-updates';

import meteorConnectionManager from '../shared/MeteorConnectionManager';

import * as environmentSettings from '../shared/Settings';

import Bugsnag from '@bugsnag/react-native'
import {initializeCloudwatch, sendToCloudWatch} from "./Logger";
import { Button, Text } from 'native-base';
let meteorUser = Meteor.user();



enableScreens();


 class App extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      authToken: null,
      authTokenRetrievalFinished: false,
      appState: '',
      refreshTries: 0,
      refreshing: true,
      lastUserDataCall: new Date().valueOf(),
      updateAvailable: false,
      isUpdating: false,
    };
    this.updateInterval = null;
  }
  waitForMeteor = () => {
      const {dispatch, userPerson} = this.props;
      const {refreshTries} = this.state;
      if (
        userPerson !== null &&
        (meteorUser === undefined || meteorUser === null)
      ) {
        if (refreshTries < 4) {
          meteorUser = Meteor.user();
          this.setState({refreshTries: this.state.refreshTries + 1}, () => {
            if (refreshTries === 3) {
              this.setState({refreshTries: 0, refreshing: false}, () => {
                console.log(
                  `meteor user undefined after ${refreshTries} attempts`,
                );
                dispatch(clearUserData());
              });
            } else {
              setTimeout(() => {
                this.waitForMeteor();
              }, 1000);
            }
          });
        }
      } else {
        console.log('meteor user is defined');
        this.setState({
          refreshTries: 0,
          refreshing: false,
        });
        dispatch(getUserData(this.state.authToken))
      }
    };
  connectMeteor = async () => {
    const serverUrl = environmentSettings.WEB_SOCKET_URL;
    const token = await this._getToken();
    if (environmentSettings.CUSTOM_BASE_SERVER) {
      const baseServer = await AsyncStorage.getItem('baseServer');
      if (baseServer && baseServer?.length > 0) {
        environmentSettings.setBaseServer(baseServer);
      }
    }
    console.log('Connecting to: ' + serverUrl);
    Meteor.connect(serverUrl);
    this.waitForMeteor();
  };

  componentDidMount = async () => {
    const {lastUserDataCall} = this.state;
    const {dispatch, offlineMode} = this.props;

    await initializeCloudwatch(this.props.user?._id)
    meteorConnectionManager.initialize();

    this.connectMeteor()

    const mData = Meteor.getData();
    mData.on('onLogin', async () => {
      const token = await this._getToken();
      dispatch(getUserData(token));
    });
    let fetchStamp = 0;
    mData.on('FETCH', async () => {
      const token = await this._getToken();
      const nowStamp = new Date().valueOf();
      if (nowStamp - fetchStamp > 5000) {
        dispatch(fetchPeopleChanges(token));
      } else
        setTimeout(
          function (passedStamp) {
            if (fetchStamp <= passedStamp) {
              dispatch(fetchPeopleChanges(token));
            }
          },
          5000,
          nowStamp,
        );
      fetchStamp = nowStamp;
    });
    const syncContainer = SyncManager.getSyncContainer();
    this.appStateSubscription = AppState.addEventListener('change', this._handleAppStateChange);
    this.props.dispatch(processBackgroundAuth());
    await this.checkForUpdates();
    this.startPeriodicUpdateCheck();
    this.startPeriodicPeopleSync();
  };

  componentWillUnmount() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    if (this.peopleSyncInterval) {
      clearInterval(this.peopleSyncInterval);
    }
  }

  _handleAppStateChange = nextAppState => {
    console.log('📱 App State Change:', this.state.appState, '->', nextAppState);

    if (nextAppState.match(/inactive|background/)) {
      this.props.dispatch(trackAppBackground());
      if (this.updateInterval) {
        clearInterval(this.updateInterval);
      }
      meteorConnectionManager.logStatus('Pre-Disconnect App State Change');
      meteorConnectionManager.disconnect();
      console.log('App entering background: Meteor disconnected');
      meteorConnectionManager.logStatus('Post-Disconnect App State Change');
    }

    if (nextAppState === 'active') {
      meteorConnectionManager.logStatus('Pre-Connect App State Change');
      this.connectMeteor();
      this.props.dispatch(clearAppBackground());
      this.props.dispatch(processBackgroundAuth());


      setTimeout(() => {
        meteorConnectionManager.logStatus('Post-Connect');
      }, 2000);
    }
    if (
      this.state.appState.match(/inactive|background/) &&
      nextAppState === 'active'
    ) {
    // check if user is logged in before rehydrating
      if (this.props.user) {
        this.props.dispatch(rehydrateUserData());
        recordAppUsage();
      }
    }

    this.setState({ appState: nextAppState });
  };

  async UNSAFE_componentWillReceiveProps(nextProps) {
    const prevUser = this.props.user;
    const nextUser = nextProps.user;

    if (
      (!prevUser && nextUser) ||
      (prevUser && nextUser && prevUser._id != nextUser._id)
    ) {
      this._getToken()
      recordAppUsage({user: nextUser, event: 'load'});
      registerIntercom(nextProps);
    }

    if (
      prevUser &&
      nextUser &&
      prevUser._id == nextUser._id &&
      (prevUser.orgId != nextUser.orgId ||
        prevUser.personId != nextUser.personId)
    ) {
      Meteor.membershipSwitch();
    }
  }

  async _getToken() {
    try {
      const token = await AsyncStorage.getItem('reactnativemeteor_usertoken');
      return token;
    } catch (err) {
      console.log('Error retrieiving token:', err);
    }
    return null;
  }

  _resetTokenState() {
    this.setState({authToken: null});
  }

  startPeriodicUpdateCheck = () => {
    this.updateInterval = setInterval(this.checkForUpdates.bind(this), 86400000);
  }

  async checkForUpdates() {
    const { user, userPerson } = this.props;
    const isLoggedIn = user && userPerson;

    try {
      if (this.state.isUpdating) return;
      this.setState({ isUpdating: true });

      console.log("Checking for updates...");
      const update = await Updates.checkForUpdateAsync();

      if (!update.isAvailable) {
        console.log("No updates available");
        this.setState({ isUpdating: false });
        return;
      }

      if (isLoggedIn) {
        // Show popup for logged-in users
        console.log("Update available! Asking logged-in user...");
        Alert.alert(
          "Update Available",
          "A new version of the app is available. Would you like to update now?",
          [
            {
              text: "Not Now",
              onPress: () => {
                console.log("Update deferred");
                this.setState({ isUpdating: false });
              },
              style: "cancel"
            },
            {
              text: "Update Now",
              onPress: async () => {
                try {
                  console.log("Downloading update...");
                  await Updates.fetchUpdateAsync();
                  console.log("Reloading app with update...");
                  await Updates.reloadAsync();
                } catch (error) {
                  console.error("Error applying update:", error);
                  this.setState({ isUpdating: false });
                }
              }
            }
          ],
          { cancelable: true }
        );
      } else {
        // Auto-update for non-logged-in users
        try {
          console.log("Update available! Auto-updating for non-logged-in user...");
          await Updates.fetchUpdateAsync();
          console.log("Update downloaded, reloading app...");
          await Updates.reloadAsync();
        } catch (error) {
          console.error("Error auto-applying update:", error);
          this.setState({ isUpdating: false });
        }
      }
    } catch (error) {
      console.error("Error checking for updates:", error);
      this.setState({ isUpdating: false });
    }
  }

  startPeriodicPeopleSync = () => {
    this.peopleSyncInterval = setInterval(() => {
      console.log("called after 2 minutes");
      // Only sync if online
      if (!this.props.offlineMode) {
        const token = this.props.resumeToken;
        if (token) {
          this.props.dispatch(fetchPeopleChanges(token));
        }
      }
    }, 120000); // 2 minutes
  };

  render() {
    const {resumeToken, user, userPerson, loading, showPinCodePrompt} = this.props;
    const {refreshTries, refreshing} = this.state;

    if (loading || (refreshing && refreshTries <= 3)) {
      return <LoadingScreen />;
    }

    if (showPinCodePrompt) {
      return <SignIn pinCodePrompt />;
    }

    if (user && userPerson) {
      const userEmail = user?.emails?.[0]?.address;

      return (
        <SafeAreaProvider>
          <Provider>
            <Root>
              <StatusBar barStyle="dark-content" backgroundColor="white" />
              <HomeScreen
                userPerson={this.props.userPerson}
                offlineMode={this.props.offlineMode}
              />
            </Root>
          </Provider>
        </SafeAreaProvider>
      );
    } else {
      return <SignIn />;
    }
  }
}

const mapStateToProps = (state) => ({
  resumeToken: state.auth.resumeToken,
  user: state.auth.userInfo,
	userPerson: state.auth.personInfo,
	userOrg: state.auth.orgInfo,
	loading: state.auth.loading,
	offlineMode: state.auth.offlineMode,
	showPinCodePrompt: state.auth.showPinCodePrompt,
});

export default connect(mapStateToProps)(App);



function recordAppUsage(options) {
	options = options || {};
	options.user = options.user || Meteor.user();
	options.event = options.event || "foreground";
	if(options.user) {
		Meteor.call("recordAppUsage", options);
	}
}

function registerIntercom(props) {
	const {user, userOrg, userPerson} = props;
	Intercom.logout();
	setTimeout( function() {
		Intercom.loginUserWithUserAttributes({email:user && user.emails && user.emails[0].address, userId: user && user._id});

		const id = DeviceInfo.getDeviceId();
		const osVersion = DeviceInfo.getSystemVersion();
		const uniqueId = DeviceInfo.getUniqueId();
		const appVersion = DeviceInfo.getVersion();

		const intercomUpdateDoc = {
			userId: user && user._id,
			email: user && user.emails && user.emails[0].address,
			name: userPerson && `${userPerson.firstName} ${userPerson.lastName}`,
			company: userOrg && userOrg.name,
			companies: [{
				id: userOrg && userOrg._id,
				name: userOrg && userOrg.name,
			}],
			customAttributes: {
				user_type: userPerson && userPerson.type,
				long_company_name: userOrg?.longName || "",
				app_hardware_id: id,
				app_os_version: osVersion,
				app_unique_id: uniqueId,
				app_version: appVersion
			}
		};
		// console.log("intercom update", intercomUpdateDoc);
		Intercom.updateUser(intercomUpdateDoc);
	}, 1500);

}



