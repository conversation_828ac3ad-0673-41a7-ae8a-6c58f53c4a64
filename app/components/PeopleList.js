import React from 'react';
import { FlatList, Text, View, TouchableOpacity, TouchableHighlight, StyleSheet, TouchableWithoutFeedback, Image, InteractionManager, Platform, Alert, Dimensions, Modal, ActivityIndicator, Keyboard, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ActionSheet, CheckBox, Header, Left, Body, Title, Right, Item, Input, Button, Content, StyleProvider, Radio, ListItem } from 'native-base';
import LoadingScreen from './LoadingScreen';
import Meteor, { withTracker } from 'react-native-meteor';
import AvatarImage from './AvatarImage';
import People from '../api/People';
import { SwipeListView, SwipeRow } from 'react-native-swipe-list-view';
import { FlatGrid } from 'react-native-super-grid';
import AsyncStorage from '@react-native-async-storage/async-storage';
import PeopleListGridView from './PeopleListGridView';
import _ from 'underscore';
import moment from 'moment';
import SharedStyles from '../shared/SharedStyles';
import {scale, verticalScale, moderateScale} from '../shared/Scaling';
import Bugsnag from '@bugsnag/react-native'

import getTheme from '../../native-base-theme/components';
import commonColor from '../../native-base-theme/variables/commonColor';
import colors from '../config/colors.json';
import Nucleo from '../components/icons/Nucleo';
import { CommonActions } from '@react-navigation/native';

import { setPeopleScope, fetchPeopleChanges } from '../actions/userData';
import { connect } from 'react-redux';
import Orgs from '../api/Orgs';
import Icon from './icons/Icon';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faXmarkCircle } from '@fortawesome/free-solid-svg-icons';

const width = Dimensions.get('window').width,
	height = Dimensions.get('window').height;

const styles = StyleSheet.create({
	touchableRow: {
		backgroundColor: colors.white,
	},
	rowFront: {
	  flex: 1,
	  padding: 12,
	  flexDirection: 'row',
	  alignItems: 'center',
	  borderBottomWidth: 1,
	  borderBottomColor: colors.charcoalLighterFade,
	},
	nameText: {
		fontSize: 16,
		fontWeight: 'bold'
	},
	labelView: {
		flex: 1,
		marginLeft: 12,
		flexDirection: 'column',
	},
	photo: {
		height: moderateScale(40),
		width: moderateScale(40),
		borderRadius: moderateScale(20),
	},
	bigPhoto: {
		height:110,
		width:110,
		borderRadius:2
	},
	checkInBox: {
		alignItems:'flex-end',
		marginLeft:'auto'
	},
	rowBack: {
		alignItems: 'flex-end',
		backgroundColor: colors.white,
		flex: 1,
		flexDirection: 'row',
		paddingRight: 15,
	},
	backRightBtn: {
		alignItems: 'center',
		bottom: 0,
		justifyContent: 'center',
		position: 'absolute',
		top: 0,
		width: 75
	},
	backRightBtnLeft: {
		backgroundColor: colors.charcoalLightFade,
		right: 75
	},
	backRightBtnRightCheckIn: {
		backgroundColor: colors.kellyGreen,
		right: 0
	},
	backRightBtnRightCheckOut: {
		backgroundColor: colors.red,
		right: 0
	},
	backTextWhite: {
		color: colors.white,
		textAlign:"center"
	},
	gridBoxContainer: {
		justifyContent: 'flex-end',
		borderRadius: 5,
		padding: 10,
		height: (width > 800) ? 160 : 150,
		backgroundColor: colors.charcoalLighterShade,
	},
	defaultText: {
		color: colors.black
	},
	listNameText: {
		fontSize: moderateScale(16),
		color: colors.black,
		fontWeight: "600",
		flex: 0,
		flexWrap: 'wrap'
	},
	listTypeText: {
		fontSize: moderateScale(12),
		color: colors.charcoalLight,
	},
	button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 4,
    backgroundColor: colors.primaryA,
    alignItems: 'center',
    justifyContent: 'center',
  },
	buttonText: {
		color: colors.white,
    fontSize: 18,
    fontWeight: 'bold'
  },
  TextInputStyleClass: {
	backgroundColor: colors.gray,
	borderRadius: 5,
	marginTop: 10,
	color: colors.black,
	paddingStart:7
   },
});

const getPeople = function(userPerson) {
	if(userPerson && userPerson.type == "family"){
	  if(!userPerson['orgId']){
		userPerson['orgId'] = Meteor.user().orgId
	  }
      const query = { 'orgId' : userPerson['orgId'] };
      if (userPerson.type === 'family' || userPerson.type === 'person') {
        let includeList = [userPerson._id];
        includeList = includeList.concat(userPerson.findRelatedFamilyPeople(true).map((p) => p._id));
        query['$or'] = [
          { '_id': { $in: includeList } },
          { 'type': { $in: ['staff', 'admin'] } }
        ];
      }
	  return People.find(query, {inActive:{$ne: true}},{
			sort: {lastName:1, firstName:1},
			fields: {firstName:1, lastName:1, checkedIn:1, checkInGroupName:1, defaultGroupId:1, checkInGroupId:1, avatarPath:1, type:1, lastMomentByType:1, familyCheckIn:1, standardOutlook: 1, "profileData.standardOutlook": 1, checkInOutlook: 1,timestamps: 1}
		});
	}else{
		return People.find({inActive:{$ne: true}},{
			sort: {lastName:1, firstName:1},
			fields: {firstName:1, lastName:1, checkedIn:1, checkInGroupName:1, defaultGroupId:1, checkInGroupId:1, avatarPath:1, type:1, lastMomentByType:1, familyCheckIn:1, standardOutlook: 1, "profileData.standardOutlook": 1, checkInOutlook: 1,timestamps: 1}
		});
	}
}

const padNumber = function(num, size) {
    var s = String(num);
    while (s.length < (size || 2)) {s = "0" + s;}
    return s;
}

let defaultFilterState = {
	selectedGroupType: "all",
	selectedOtherGroupId: null,
	selectedOtherGroupName: "",
	selectedPersonTypes: ["people", "staffAndAdmins", "families"],
	selectedSortType: "alphabetical",
	selectedViewStyle: "list"
};

const isCheckInAble = (person) => { return person.type=="person" || person.type=="staff" || person.type=="admin";};

const SwipeableButtons = ({item, rowMap, selectRow, closeRow}) => {

	if (item.type == "person" || item.type=="staff" || item.type=="admin") {
		if (item.checkedIn) {
			return (
				<View style={styles.rowBack}>
					<TouchableOpacity style={[styles.backRightBtn, styles.backRightBtnLeft]} onPress={ _ =>  {closeRow(); selectRow(item._id, 'move', rowMap) } }>
						<Text style={styles.backTextWhite}>Move</Text>
					</TouchableOpacity>
					<TouchableOpacity style={[styles.backRightBtn, styles.backRightBtnRightCheckOut]} onPress={ _ => {closeRow(); selectRow(item._id, 'checkout', rowMap) } }>
						<Text style={styles.backTextWhite}>Check Out</Text>
					</TouchableOpacity>
				</View>
			)
		} else {
			return (
				<View style={styles.rowBack}>
					<TouchableOpacity style={[styles.backRightBtn, styles.backRightBtnRightCheckIn]} onPress={ _ => {closeRow(); selectRow(item._id, 'checkin', rowMap) } }>
						<Text style={styles.backTextWhite}>Check In</Text>
					</TouchableOpacity>
				</View>
			)
		}
	} else {
		return (
			<View style={styles.rowBack}></View>
		)
	}
}

class PeopleListItem extends React.PureComponent {
  setRef = ref => {
    this.rowRef = ref;
  };

  closeRow = () => {
    this.rowRef.closeRow();
  };
  renderRecuuringTime = ({item}) => {
    return (
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <Nucleo name="icon-calendar-date" size={12} color="#3AC6EF" />
        <Text style={[styles.listTypeText, {marginLeft: 5}]}>
          {item.scheduleType && item.scheduleType + ' -'}
        </Text>
        <Text style={[styles.listTypeText, {marginLeft: 3}]}>
          {item.scheduledDays.join(',')}
        </Text>
      </View>
    );
  };


  render() {
    const {
      isStaffOrAdmin,
      item,
      selectMode,
      rowMap,
      selectRow,
      onPress,
      selectPerson,
      isPersonSelected,
    } = this.props;
    const outlookData = item.profileData
      ? item.profileData.standardOutlook
      : item.standardOutlook;
    const hasAllergy =
      isStaffOrAdmin &&
      outlookData &&
      outlookData.allergies &&
      outlookData.allergies.trim().length > 0
        ? true
        : false;
    const hasNote =
      isStaffOrAdmin &&
      outlookData &&
      outlookData.importantNotes &&
      outlookData.importantNotes.trim().length > 0
        ? true
        : false;
    const hasSpecialNeeds =
      isStaffOrAdmin &&
      outlookData &&
      outlookData.specialNeeds &&
      outlookData.specialNeeds.trim().length > 0
        ? true
        : false;
    const hasIANotes =
      isStaffOrAdmin && item?.checkInOutlook && item?.checkInOutlook?.length > 0
        ? true
        : false;
    const hasRecurringDays =
      isStaffOrAdmin && (item.type == 'person' || item.type == 'staff');
    return (
      <SwipeRow
        key={item._id}
        disableRightSwipe={!isStaffOrAdmin || selectMode}
        disableLeftSwipe={!isStaffOrAdmin || !isCheckInAble(item)}
        rightOpenValue={isCheckInAble(item) ? (item.checkedIn ? -150 : -75) : 0}
        ref={this.setRef}>
        {SwipeableButtons({
          item,
          rowMap,
          selectRow: selectRow,
          closeRow: this.closeRow,
        })}

        <TouchableHighlight
          testID="people-list-item"
          onPress={() => onPress(item._id, item)}
          style={styles.touchableRow}
          underlayColor={colors.grayDark}>
          <View style={styles.rowFront}>
            <View style={{flexDirection: 'column', flex: 1}}>
              <View style={{flexDirection: 'row'}}>
                <View
                  style={{width: moderateScale(50), height: moderateScale(50)}}>
                  {selectMode ? (
                    isCheckInAble(item) && (
                      <TouchableWithoutFeedback
                        onPress={() => selectPerson(item._id)}
                        checked={() => isPersonSelected(item._id)}>
                        <View
                          style={{
                            width: moderateScale(40),
                            height: moderateScale(40),
                            borderRadius: moderateScale(20),
                            borderColor: colors.primaryA,
                            backgroundColor: isPersonSelected(item._id)
                              ? colors.primaryA
                              : colors.white,
                            borderWidth: 2,
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          {isPersonSelected(item._id) && (
                            <Icon
                              name="md-checkmark"
                              style={{
                                fontSize: moderateScale(34),
                                color: colors.white,
                                fontWeight: 'bold',
                              }}
                            />
                          )}
                        </View>
                      </TouchableWithoutFeedback>
                    )
                  ) : (
                    <AvatarImage
                      source={item.getAvatarUrl && item.getAvatarUrl()}
                      initials={item.personInitials && item.personInitials()}
                      style={styles.photo}
                    />
                  )}
                </View>
                <View style={styles.labelView}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <Text style={styles.listNameText}>
                      {item.firstName} {item.lastName}{' '}
                    </Text>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      {hasAllergy && (
                        <Nucleo
                          name="icon-hospital-32"
                          size={12}
                          color={colors.red}
                        />
                      )}
                      {(hasNote || hasSpecialNeeds) && (
                        <Nucleo
                          name="icon-c-info"
                          size={12}
                          style={{marginLeft: 8}}
                          color={colors.grayPlatform}
                        />
                      )}
                      {hasIANotes && (
                        <Nucleo
                          name="icon-check-in"
                          size={12}
                          style={{marginLeft: 8}}
                          color={colors.grayPlatform}
                        />
                      )}
                    </View>
                  </View>
                  <View style={{flexDirection: 'column', marginTop: 2}}>
                    {item?.familyCheckIn?.absent ? (
                      <View style={{flexDirection: 'column'}}>
                        <Text style={styles.listTypeText}>
                          {item?.familyCheckIn?.absentReason}
                        </Text>
                        {hasRecurringDays &&
                        item.recurringDays &&
                        item.recurringDays.length > 0 ? (
                          <FlatList
                            data={item.recurringDays}
                            renderItem={this.renderRecuuringTime}
                            keyExtractor={(item, index) => index.toString()}
                          />
                        ) : (
                          hasRecurringDays && (
                            <View style={{flexDirection: 'row'}}>
                              <Nucleo
                                name="icon-calendar-date"
                                size={12}
                                color="#3AC6EF"
                              />
                              <Text
                                style={[styles.listTypeText, {marginLeft: 5}]}>
                                No active schedule
                              </Text>
                            </View>
                          )
                        )}
                      </View>
                    ) : (
                      <View style={{flexDirection: 'row', marginTop: 2}}>
                        <View style={{flexDirection: 'column'}}>
                          <Text style={styles.listTypeText}>
                            {capitalize(item.translatedPersonType())}
                          </Text>

                          {hasRecurringDays &&
                          item.recurringDays &&
                          item.recurringDays.length > 0 ? (
                            <FlatList
                              style={{flexDirection: 'column'}}
                              data={item.recurringDays}
                              renderItem={this.renderRecuuringTime}
                              keyExtractor={(item, index) => index.toString()}
                            />
                          ) : (
                            hasRecurringDays && (
                              <View style={{flexDirection: 'row'}}>
                                <Nucleo
                                  name="icon-calendar-date"
                                  size={12}
                                  color="#3AC6EF"
                                />
                                <Text
                                  style={[
                                    styles.listTypeText,
                                    {marginLeft: 5},
                                  ]}>
                                  No active schedule
                                </Text>
                              </View>
                            )
                          )}
                        </View>
                      </View>
                    )}
                  </View>
                </View>
                {item.checkedIn && (
                  <View style={styles.checkInBox}>
                    <Icon
                      type="FontAwesome5"
                      name="check"
                      style={{color: colors.greenLight, fontSize: 18}}></Icon>
                    {item.checkInGroupName ? (
                      <Text style={[styles.defaultText, {fontSize: 12}]}>
                        {item.checkInGroupName}
                      </Text>
                    ) : null}
                  </View>
                )}
                {isCheckInAble(item) &&
                  !item.checkedIn &&
                  !item?.familyCheckIn?.absent && (
                    <View style={styles.checkInBox}>
                      <Icon
                        type="FontAwesome5"
                        name="check"
                        style={{
                          color:
                            item.absent() || item.reservationCancellationToday()
                              ? `${colors.purple}BF`
                              : item.currentFamilyCheckin()
                              ? colors.yellowLight
                              : colors.charcoalLightFade,
                          fontSize: 18,
                        }}></Icon>
                    </View>
                  )}
              </View>
              {isStaffOrAdmin &&
                isCheckInAble(item) &&
                item.checkedIn &&
                item.eventMessages && (
                  <View style={{flexDirection: 'row'}}>
                    <View style={{width: moderateScale(60)}}></View>
                    <View style={{flexDirection: 'row'}}>
                      {item.eventMessages.map(i => (
                        <Text
                          key={i.label}
                          style={{
                            width: moderateScale(80),
                            fontSize: moderateScale(11),
                            color: i.alert
                              ? colors.redBright
                              : colors.charcoalLight,
                          }}>
                          {i.label}:{'\n'}
                          {i.value}
                        </Text>
                      ))}
                    </View>
                  </View>
                )}
            </View>
          </View>
        </TouchableHighlight>
      </SwipeRow>
    );
  }
}

class PeopleList extends React.PureComponent {
	constructor(props) {
		super(props);

		this.openRowRefs = [];

		if (props.userPerson) {
			if (props.userPerson.type != "family") {
				defaultFilterState.selectedPersonTypes = ["people", "staffAndAdmins"];
				defaultFilterState.selectedSortType = "checkedIn";
			}
		}

		this.state = {
			filterState: defaultFilterState,
			savedStateLoaded: false,
			selectMode: false,
			selectedPeople: [],
			searchText: "",
			searchFilterOverride: false,
			showSearch: false,
			isViewReady: false,
			nameToFacePersons: [],
			nameToFaceModalVisible: false,
			savingNameToFace: false,
			rerenderDate: new Date().valueOf(),
			isInCompleteNametoFaceModalVisible:false,
			selectedNametoFaceOptionHasError:false,
			selectedNametoFaceOption:"",
			displayReason:""
		}
		this.props.navigation.dispatch(CommonActions.setParams({ currentFilterState: defaultFilterState, userPerson: props.userPerson }));
	}
	UNSAFE_componentWillReceiveProps(nextProps) {
		const prevPerson = this.props.userPerson;
		const nextPerson = nextProps.userPerson;

		if (prevPerson && nextPerson && prevPerson._id != nextPerson._id ) {
			this.props.navigation.dispatch(CommonActions.setParams({userPerson: nextPerson}));
			if (nextPerson.type=="staff" || nextPerson.type=="admin") {
				defaultFilterState.selectedPersonTypes = ["people", "staffAndAdmins"];
				defaultFilterState.selectedSortType = "checkedIn";
			}
			if (!this.state.savedStateLoaded) {
				this.setState({filterState: defaultFilterState});
			}
		}
		if (this.props.currentOrg?._id != nextProps.currentOrg?._id) {
			this.setState({filterState: defaultFilterState, searchText: ''});
			this._handleTitle(defaultFilterState);
		}
	}

	_setRerenderDate = () => {
		this.setState({rerenderDate: new Date().valueOf()});
	}

	_onPress = async (personId, person) => {
		if (!this.state.selectMode) {
			InteractionManager.runAfterInteractions(() => {
				this.props.navigation.navigate('Person', {
					currentOrg: this.props.currentOrg,
          personId,
          passedPerson: person,
          getRatios: this.getRatios,
        });
			});
		}
	};

	_onGridPress = (person) => {
		if (this.state.selectMode && (person.type=="staff" || person.type=="person" || person.type=="admin"))
			this._selectPerson(person._id);
		else if (!this.state.selectMode)
			this._onPress(person._id, person);
	}

	_onEnterSelectMode = () => {
		this.setState({selectMode: true, selectedPeople: []});
		this.props.navigation.dispatch(CommonActions.setParams({ activeSelectMode: true}));
	}

	_onFinishSelectMode = () => {

		if (this.state.selectedPeople.length > 0) {
			let options = ["Move", "Check-In", "Check-Out", "Tag In Moment"];
			if (this.props.currentOrg && this.props.currentOrg.isChildCare()) options.push("Sleep Check");

			options.push("Cancel");
			ActionSheet.show(
				{
					options,
					cancelButtonIndex: options.length-1,
					title: "Choose Action"
				},
				buttonIndex => {
					ActionSheet.hide();

					console.log("buttonIndex:", buttonIndex, "people:", this.state.selectedPeople);
					console.log("selected option:", options[buttonIndex]);
					this.setState({selectMode: false});
					this.props.navigation.dispatch(CommonActions.setParams({ activeSelectMode: false}));
					if (buttonIndex == 0) {
						const callMoveOptions = () => { this.showMoveOptions({selectedPeople: this.state.selectedPeople}); }
						if (Platform.OS === 'android')
							setTimeout(  callMoveOptions, 1000);
						else
							callMoveOptions();
					}
					else if (buttonIndex == 1) {
						Meteor.call("checkInMultiple", {selectedPeople: this.state.selectedPeople}, (error, result) => {
							if (error) {
								alert ("Error: " + error.reason);
							} else {
								alert("Success: Checked in " + result.checkInCount);
							}
						});
					}
					else if (buttonIndex == 2) {
						Meteor.call("checkOutMultiple", {selectedPeople: this.state.selectedPeople}, (error, result) => {
							if (error) {
								alert ("Error: " + error.reason);
							} else {
								alert("Success: Checked out " + result.checkInCount);
							}
						});
					}
					else if (options[buttonIndex] == "Tag In Moment") {
						this.props.navigation.navigate('MomentEntryModal', {newMomentType: "other", taggedPeople: this.state.selectedPeople});
					}
					else if (options[buttonIndex] == "Sleep Check") {
						const totalSleepChecks = this.state.selectedPeople.length;
						if (totalSleepChecks < 1) {
							alert("You must select at least one person for sleep check.");
							return;
						}
						Alert.alert('Confirm Sleep Check',
							'This will create sleep check(s) for ' + totalSleepChecks + ' people.',
							[
								{text: 'Cancel', style:'cancel'},
								{	text: 'OK',
									onPress: () => {
										Meteor.call("sleepCheck", {selectedPeople: this.state.selectedPeople}, (error, result) => {
											if (error) {
												alert ("Error: " + error.reason);
											} else {
												const outMessage = (result.successfulSleepChecks ? "Successful sleep checks: " + result.successfulSleepChecks.length + "\n" : "") +
													(result.sleepNotFound ? "Sleep not found: " + result.sleepNotFound.length + "\n" : "");
												alert(outMessage);
											}
										});
									}
								}
							],
							{cancelable: false}
						);
					}
				}
			);
		} else {
			this.setState({selectMode: false});
			this.props.navigation.dispatch(CommonActions.setParams({ activeSelectMode: false}));
		}
	}

	componentDidMount() {
		this.getRatios()
		InteractionManager.runAfterInteractions(() => {
			this.setState({
				isViewReady: true
			})
		});

		this.props.navigation.dispatch(CommonActions.setParams({
			onSaveFilters: this._onSaveFilters,
			onEnterSelectMode: this._onEnterSelectMode,
			onFinishSelectMode: this._onFinishSelectMode
		}));
		const self = this;
		const user = Meteor.user();
		const filterString = user ? `filterState_${user._id}` : "filterState";
		try {
			AsyncStorage.getItem(filterString).then(value => {
				return JSON.parse(value);
			})
			.then((result) => {
				if (result) {
					//console.log("receiving initial filterstate", result);
					self.setState({filterState:result, savedStateLoaded: true});
					self._handleTitle(result);
					self.props.navigation.dispatch(CommonActions.setParams({ currentFilterState: result}));
				} else {
					self.props.dispatch(setPeopleScope("all"));
				}
			});
		} catch(e) {
			console.log("Error setting filter state", e);
			Bugsnag.notify(e);
		}
	}

	_getNameToFacePeople = (filterGroupId) => {
		Meteor.call("nameToFacePeopleFilter", { filterGroupId }, (err, result) => {
			if (err) {
				if (err.message) {
					Alert.alert(err.message)
				} else {
					Alert.alert("Error", "Could not retrieve Name to Face data");
				}
				return this._closeNameToFaceModal();
			} else {
				const ntfPeople = People.find({_id: {$in: result.peopleIds}}, {sort: {firstName: 1}});

				if (ntfPeople.length == 0) {
					Alert.alert("No Results", "Move yourself to the group to perform Name to Face");
					return this._closeNameToFaceModal();
				}

				this.setState({nameToFacePersons: ntfPeople})
			}
		})
	}

	_closeNameToFaceModal = () => {
		this.getRatios()
		this.setState({
			nameToFaceModalVisible: false,
			savingNameToFace: false,
			nameToFacePersons: [],
			selectMode: false,
			selectedPeople: [],
		})
	};

	_openNameToFaceModal = () => {
		Keyboard.dismiss()
		setTimeout(() => {
			let filterGroupId = null;
			if (this.state.filterState.selectedGroupType == "mygroup") {
				filterGroupId = this.props.userPerson.checkInGroupId || this.props.userPerson.defaultGroupId;
			} else if (this.state.filterState.selectedGroupType == "other") {
				filterGroupId = this.state.filterState.selectedOtherGroupId;
			}

			if (!filterGroupId) {
				Alert.alert("No current selected group", "Please choose a group before submitting a name-to-face check.");
				return;
			}
			this.props.navigation.dispatch(CommonActions.setParams({ activeSelectMode: false}));
			this.setState({ nameToFaceModalVisible: true, selectMode: true, selectedPeople: [], savingNameToFace: false });
			this._getNameToFacePeople(filterGroupId);
		}, 500)
	};

	_saveNameToFace = (isFromSaveNameToFace) => {
		let filterGroupId = null;
		if (this.state.filterState.selectedGroupType == "mygroup") {
			filterGroupId = this.props.userPerson.checkInGroupId || this.props.userPerson.defaultGroupId;
		} else if (this.state.filterState.selectedGroupType == "other") {
			filterGroupId = this.state.filterState.selectedOtherGroupId;
		}

		if (!filterGroupId) {
			Alert.alert("No current selected group", "Please choose a group before submitting a name-to-face check.");
			return;
		}

		const selectedPeople = this.state.selectedPeople || [];
		const totalPeople = this.state.nameToFacePersons || [];

		let inComplete = this.props.currentOrg && this.props.currentOrg.hasCustomization("moments/incompleteFTF/enabled")
		inComplete = !inComplete;
		var moment_type = this.state.selectedNametoFaceOption == "Other" ? this.state.displayReason : this.state.selectedNametoFaceOption
		var isLessPeopleSelected = selectedPeople.length < totalPeople.length
		if(isFromSaveNameToFace && inComplete && isLessPeopleSelected){
			this.setState({
				selectedNametoFaceOption:"",
				displayReason:"",
				nameToFaceModalVisible:false,
				isInCompleteNametoFaceModalVisible:true
			})
			return
		}else{
			this.setState({savingNameToFace: true})
		}

		var finalizeNameToFace = ( completedById ) => {
			// console.log("finalizing");
			Meteor.call("nameToFaceCheckConfirmation", { selectedPeople, completedById, filterGroupId, inComplete, moment_type }, (error, result) => {
				if (error) {
					Alert.alert("Name-to-Face Issue(s)", error.reason);
					this.setState({savingNameToFace: false});
				} else {
					Alert.alert("Successful Name-to-Face", "Completed successful Name-to-Face check");
					this._closeNameToFaceModal();
				}
			});
		};

		if (this.props.currentOrg && this.props.currentOrg.hasCustomization("people/nametoface/requiresCompletedBy")) {
			const peopleToFilter = getPeople(this.props.userPerson);
			const staffPeople = _.filter(peopleToFilter, (p) => { return p.checkedIn && ( p.type=="staff" || p.type=="admin" ); }),
				staffPeopleList = staffPeople.map( (p) => { return p.lastName + ", " + p.firstName; });

			var callChooseStaff = () => {
				ActionSheet.show(
					{
						options: staffPeopleList.concat("Cancel"),
						cancelButtonIndex: staffPeopleList.length,
						title: "Completed By"
					},
					buttonIndex => {
						// console.log(buttonIndex);
						if (buttonIndex == staffPeople.length) {
							this.setState({savingNameToFace: false});
							return;
						}
						ActionSheet.hide();
						finalizeNameToFace(staffPeople[buttonIndex]._id);
					}
				);
			}

			if (Platform.OS === 'android') {
				setTimeout(  callChooseStaff, 1000);
			} else {
				callChooseStaff();
			}

		} else {
			finalizeNameToFace();
		}
	}

	_onSaveFilters = async (filterState) =>  {
		this.setState({filterState});
		const user = Meteor.user();
		const filterString = user ? `filterState_${user._id}` : "filterState";
		try {
			await AsyncStorage.setItem(filterString, JSON.stringify(filterState));
		} catch(e) {
			Bugsnag.notify(e);
		}
		//console.log("setting savestate", filterState);
		this.props.navigation.dispatch(CommonActions.setParams({ onSaveFilters: this._onSaveFilters, currentFilterState: filterState }));

		this._handleTitle(filterState);
		this.getRatios()
	}

	async _handleTitle(filterState) {
		let screenTitle = "People", scopeLabel = "all";
		switch(filterState && filterState.selectedGroupType) {
			case "other":
				screenTitle = filterState.selectedOtherGroupName;
				scopeLabel = "other|" + filterState.selectedOtherGroupId;
				break;
			case "mygroup":
				screenTitle = filterState.myGroupName;
				scopeLabel = "mygroup|0";
				break;
		}

		this.props.navigation.dispatch(CommonActions.setParams({peopleListTitle: screenTitle}));
		this.props.dispatch(setPeopleScope(scopeLabel));
		const token = await this._getToken();
		this.props.dispatch(fetchPeopleChanges(token))
	}

	async _getToken() {
		try {
			const token = await AsyncStorage.getItem('reactnativemeteor_usertoken');
			return token;
		} catch (err) {
			console.log("Error retrieiving token:", err);
		}
		return null;
	}

	_isPersonSelected(personId) {
		return _.contains(this.state.selectedPeople, personId);
	}

	_selectPerson(personId) {
		let joined;
		if (_.contains(this.state.selectedPeople, personId)) {
			joined = _.filter(this.state.selectedPeople, (p) => { return p != personId;});
		} else {
			joined = this.state.selectedPeople.concat(personId);
		}
		this.setState({ selectedPeople: joined });
	}

	selectRow(rowKey, action, rowMap) {

		const person = Meteor.collection('people').findOne(rowKey);
		switch(action) {
			case 'checkin':
				this.props.navigation.navigate('CheckInModal', {
					person: person,
					rerender: this._setRerenderDate,
					getRatios: this.getRatios
				});
				break;
			case 'checkout':
				this.props.navigation.navigate('CheckOutModal', {
					person: person,
					rerender: this._setRerenderDate,
					getRatios: this.getRatios
				});
				break;
			case 'move':
				this.showMoveOptions({personId: person._id});
				break;
		}
	}

	showMoveOptions(options) {
		const {groups} = this.props;

		ActionSheet.show(
			{
				options: groups.map( (g) => { return g.name;}).concat(["Cancel"]),
				cancelButtonIndex: groups.length,
				title: "Move to..."
			},
			buttonIndex => {

				if (buttonIndex < groups.length) {
					const selectedGroup = groups[buttonIndex];
					var switchData = {
						personId: options.personId,
						peopleIds: options.selectedPeople,
						groupId: selectedGroup._id,
						groupName: selectedGroup.name,
						switchAllInGroup: false
					};

					Meteor.call('switchGroup', switchData, (error, result) => {
						if (error) {
							alert(error.reason);
						} else {
							if (result.currentPersonSwitched) {
								var filterState = {...this.state.filterState}
								filterState.selectedOtherGroupId = selectedGroup._id;
								filterState.selectedOtherGroupName = selectedGroup.name;
								filterState.selectedGroupType = "other";
								this.setState({filterState});
								this.props.navigation.dispatch(CommonActions.setParams({ currentFilterState: filterState }));
								this._handleTitle(filterState);
								this.getRatios()
							}
						}
					});
				}
			}
		);
	}

	showGroupChooser() {
		Keyboard.dismiss()
		setTimeout(() => {
			this.props.navigation.navigate('GroupListModal', {onModalDismiss: (selectedGroup) => {
				var filterState = {...this.state.filterState}
				if (selectedGroup) {
					filterState.selectedOtherGroupId = selectedGroup._id;
					filterState.selectedOtherGroupName = selectedGroup.name;
					filterState.selectedGroupType = "other";
				} else {
					filterState.selectedGroupType = "all";
					filterState.selectedOtherGroupId = null;
					filterState.selectedOtherGroupName = null;
				}/*
				this.setState({filterState});

				this.props.navigation.dispatch(CommonActions.setParams({ currentFilterState: filterState }));

				this._handleTitle(filterState);*/
				this._onSaveFilters(filterState);
			}});
		}, 500)
	}

	recentMomentData(person) {
		if (!person || person.type != "person") return;
		const group = _.find( this.props.groups, (g) => { return g._id == person.defaultGroupId || g._id == person.checkInGroupId;});
		if (!group) return;

		const startOfDay = new moment().startOf('day').valueOf();
		const recentMomentTypes = ["potty", "food", "sleep"];
		let recentMomentData = [];
		_.each(recentMomentTypes, (mt) => {
			if (person.lastMomentByType && person.lastMomentByType[mt]) {

				const thisMoment = person.lastMomentByType[mt];
				const x = moment(thisMoment.sortStamp), y = moment(), z = moment.duration(y - x);
				//const i18NValue = TAPi18n.__('momentTypes.' + mt + '.prettyName', {defaultValue: thisMoment.momentTypePretty}) ;
				const gsRule = group && _.find(group.groupSuggestionRules, (gsr) => { return gsr.momentType == mt;});

				if (x > startOfDay) {
					recentMomentData.push({
						type: thisMoment.momentTypePretty, //i18NValue,
						recency: z.days() > 0 ? z.humanize() + " ago" : z.hours() + ":" + padNumber(z.minutes(), 2),
						overLimit: gsRule && gsRule.thresholdHours && (z.minutes() >= (gsRule.thresholdHours * 60))
					});
				}
			}
		});
		return recentMomentData;
	}

	checkValidationsForNameToFace = async() => {
		if(this.state.selectedNametoFaceOption){
			if(this.state.selectedNametoFaceOption == "Other" && !this.state.displayReason.trim()){
				this.setState({selectedNametoFaceOptionHasError:true})
			}else{
				this.setState({ selectedNametoFaceOptionHasError:false, isInCompleteNametoFaceModalVisible: false, nameToFaceModalVisible:true })
				setTimeout(() => {
					this._saveNameToFace(false)
				}, 800)
			}
		}else{
			this.setState({selectedNametoFaceOptionHasError:true})
		}
	}

	listRenderItem = ({item}, rowMap) => {
		const
			userPerson = this.props.userPerson || {},
			isStaffOrAdmin = !_.isEmpty(userPerson) && (userPerson.type == "staff" || userPerson.type == "admin");

		const peopleListItem = (
			<PeopleListItem
				item={item}
				isStaffOrAdmin={isStaffOrAdmin}
				rowMap={rowMap}
				selectRow={this.selectRow.bind(this)}
				onPress={this._onPress}
				selectMode={this.state.selectMode}
				selectPerson={this._selectPerson.bind(this)}
				isPersonSelected={this._isPersonSelected.bind(this)}
			/>
		);

		if (isStaffOrAdmin && (item.isFirstScheduled || item.isFirstUnscheduled || item.isFirstAbsent)) {
			const label = item?.isFirstAbsent ? "Absent Today" : item.isFirstScheduled ? "Scheduled Today" : "Not Scheduled Today";
			return (
				<View>
					<View style={styles.rowFront}>
						<View style={{flexDirection:"column", flex: 1}}>
							<Text style={styles.listNameText}>{label}</Text>
						</View>
					</View>
					{peopleListItem}
				</View>
			);
		}

		return peopleListItem;
	}

	listKeyExtractor = (item, index) => item._id

	getRatios = () => {
		Meteor.call("getPeopleCountForGroup", (error, result) => {
			if (error) {
				console.log("Error getting data widget ratios", error);
			} else {
				this.setState({checkinRatios: result});
			}
		})
	}

	render() {

		const {
      subsLoading,
      recurringReservations,
      groups,
      absentPeople,

    } = this.props;
		const {rerenderDate, checkinRatios} = this.state;
		if (subsLoading && !checkinRatios) {
      return <LoadingScreen />;
    }
		const filterState = this.state.filterState,
			userPerson = this.props.userPerson || {},
			activeFilters = filterState && ( filterState.selectedGroupType != "all" || filterState.selectedPersonTypes.length < 3),
			isStaffOrAdmin = !_.isEmpty(userPerson) && (userPerson.type == "staff" || userPerson.type == "admin");

		const people = getPeople(userPerson);

		let filterGroupId, filteredGroupName = "All Groups";
		if (filterState.selectedGroupType == "mygroup") {
			filterGroupId = userPerson && (userPerson.checkInGroupId || userPerson.defaultGroupId);
			filteredGroupName = filterState.myGroupName;
		}
		else if (filterState.selectedGroupType == "other") {
			filterGroupId = filterState.selectedOtherGroupId;
			filteredGroupName = filterState.selectedOtherGroupName;
		}

		const searchText = this.state.searchText ? this.state.searchText.toLowerCase() : "",
			searchFilterOverride = this.state.searchFilterOverride;

		let nameToFacePrompt = false

		if(isStaffOrAdmin && filterGroupId){
			const selectedGroup = groups.find(item => item._id == filterGroupId);
			const lastNameToFaceMomentTimestamp = selectedGroup?.timestamps?.nameToFaceMomentTimestamp?.timestamp ? selectedGroup?.timestamps?.nameToFaceMomentTimestamp?.timestamp : 0
			const peopleGroup =  _.filter(people, (person) => {
				return person?.checkedIn && (filterGroupId == person?.checkInGroupId || filterGroupId == person?.timestamps?.moveMomentTimestamp?.oldGroupId) && (filterGroupId == person?.timestamps?.moveMomentTimestamp?.groupId || filterGroupId == person?.timestamps?.moveMomentTimestamp?.oldGroupId) && (person?.timestamps?.moveMomentTimestamp?.timestamp >= lastNameToFaceMomentTimestamp);
			});
			const peopleGroupNameToFace =  _.filter(people, (person) => {
				return person?.checkedIn && filterGroupId == person?.checkInGroupId;
			});
			nameToFacePrompt = peopleGroup && peopleGroup.length > 0 && peopleGroupNameToFace && peopleGroupNameToFace.length > 0
		}

		let filteredPeople;
		if (userPerson.type == "family")
			filteredPeople = _.filter( people, (person) => {
				return (person.type == "family" || person.type == "person");
			});
		else
			filteredPeople = _.filter( people, (person) => {
				return (!filterGroupId || searchFilterOverride || filterGroupId == person.checkInGroupId || filterGroupId == person.defaultGroupId) &&
					( searchText == "" || (person.firstName + ' ' + person.lastName).toLowerCase().indexOf(searchText)>=0) &&
						( searchFilterOverride ||
							( person.type == "person" && _.contains(filterState.selectedPersonTypes, "people") ) ||
						  ( person.type == "family" && _.contains(filterState.selectedPersonTypes, "families") ) ||
						  ( (person.type == "staff" || person.type == "admin") && _.contains(filterState.selectedPersonTypes, "staffAndAdmins")) );

			});

		const todayStamp = new moment().valueOf();
		const endStamp = new moment().startOf("day").valueOf();

		recurringReservations?.filter(rr =>
			(!rr.scheduledDate || rr.scheduledDate <= todayStamp) &&
			(!rr.scheduledEndDate || rr.scheduledEndDate > endStamp)
		)

		const sortedPeople = _.chain(filteredPeople)
			.sortBy("firstName")
			.sortBy("lastName")
			.sortBy( (person) => {  return (filterState.selectedSortType == "checkedIn" ?
					(person.checkedIn ? (person.checkInGroupId == filterGroupId ? "0" : "1") : "2")
				: "");
			})
			.sortBy( (person) => {
				const dayOfWeek = new moment().format("ddd").toLowerCase();
				person.isScheduledToday = _.find(recurringReservations, rr =>
						rr.selectedPerson == person._id &&
						(!rr.scheduledDate || rr.scheduledDate <= todayStamp) &&
						(!rr.scheduledEndDate || rr.scheduledEndDate > endStamp) && (!rr.recurringDays || rr.recurringDays?.some(item => dayOfWeek === item)),
						);
				return (isStaffOrAdmin && person.isScheduledToday) ? 0 : 1;
			})
			.sortBy( (person) => {  return (person?.familyCheckIn?.absent == true ? 0 : 1);})
			.value();

		let hasFirstScheduled = false, hasFirstUnscheduled = false, isFirstAbsent = false;
		const prefixedPeople = _.each(sortedPeople, (person) => {
			if (isStaffOrAdmin) {
				if (person?.familyCheckIn?.absent) {
					if(!isFirstAbsent)
						person.isFirstAbsent = true;
					isFirstAbsent = true;
					return
				}
				if (person.isScheduledToday && !hasFirstScheduled) {
					person.isFirstScheduled = true;
					hasFirstScheduled = true;
				}
				if (!person.isScheduledToday && !hasFirstUnscheduled) {
					person.isFirstUnscheduled = true;
					hasFirstUnscheduled = true;
				}
			}
		});

		const labeledPeople = _.map(prefixedPeople, (person) => {
			if (person.type=="person" || person.type=="staff") {
				person.eventMessages = _.map(this.recentMomentData(person), (md) => { return {label:md.type, value:md.recency, alert: md.overLimit};});
				if (isStaffOrAdmin) {
					const dayMap = {
						"sun": {l: "Su", v: false},
						"mon": {l: "M", v: false},
						"tue": {l: "T", v: false},
						"wed": {l: "W", v: false},
						"thu": {l: "R", v: false},
						"fri": {l: "F", v: false},
						"sat": {l: "Sa", v: false}
					};
					let recurringTime = [];
					(recurringReservations || []).filter(rr =>
							rr.selectedPerson == person._id &&
							(!rr.scheduledDate || rr.scheduledDate <= todayStamp) &&
							(!rr.scheduledEndDate || rr.scheduledEndDate > endStamp)
						).forEach( rr => {
							const objectDays = {
								scheduleType: this.props?.currentOrg?.valueOverrides?.scheduleTypes?.find(data => data?._id === rr?.scheduleType)?.type || "",
								scheduledDays: []
							};
							(rr.recurringDays || []).forEach( rd => {
								objectDays?.scheduledDays?.push(dayMap[rd]?.l)
							});
							(rr.recurringDays || []).length>0 && recurringTime.push(objectDays)



					});

					person.recurringDays =recurringTime;
				}
			}


			return person;
		});

		let groupStatusLabel;
		if (isStaffOrAdmin && filterGroupId && checkinRatios) {
			const groupCheckInData = checkinRatios.filter(gcr => gcr._id == filterGroupId)[0]?.counts ?? {
				checkedInCount: 0,
				absentCount: 0,
				remainingCount: 0,
				staffCheckedInCount: 0,
			};
			const { checkedInCount, absentCount, remainingCount, staffCheckedInCount } = groupCheckInData
			groupStatusLabel = checkedInCount + " check-in"
			if (absentCount > 0) groupStatusLabel += " | " + absentCount + ' absent';
			groupStatusLabel += " | " + remainingCount + " remaining";
			groupStatusLabel += " | " + staffCheckedInCount + " staff"
		}

		return (
			<StyleProvider style={getTheme(commonColor)}>
				<View  testID={"people-list"} style={{flex: 1, backgroundColor: colors.white}}>
				{isStaffOrAdmin && (
					<View
					style={{
						flexDirection: 'row',
						justifyContent: 'space-between',
						borderBottomWidth: 1,
						borderBottomColor: colors.charcoalLightFade,
						paddingBottom: 4,
					}}>
					<View style={{flexDirection: 'row'}}>
						<Button
						bordered
						style={{marginTop: 4, marginLeft: 4, padding: 4}}
						onPress={() => this.showGroupChooser()}>
						<Text testID='group-btn' style={{fontSize: 16, color: colors.primaryA}}>
							{filteredGroupName}
						</Text>
						</Button>
						<Button
							testID='search-btn'
							bordered={!this.state.showSearch}
							style={{marginTop: 4, marginLeft: 4, padding: 4}}
							onPress={() =>
								this.setState({showSearch: !this.state.showSearch})
						}>
						<Icon
							name="ios-search"
							 type="FontAwesome5"
                           
							style={{
							color: this.state.showSearch
								? colors.white
								: colors.primaryA,
							}}
						/>
						</Button>
					</View>
					<View style={{flexDirection: 'row'}}>
						<Button
						bordered
						style={{marginTop: 4, marginRight: 4, padding: 4}}
						onPress={() => this._openNameToFaceModal()}>
						<Text style={{fontSize: 16, color: colors.primaryA}}>
							Name to Face
						</Text>
						</Button>
					</View>
					</View>
				)}
				{isStaffOrAdmin && groupStatusLabel ? (
					<View
					style={{
						width: '100%',
						alignItems: 'center',
						justifyContent: 'center',
						backgroundColor: colors.primaryA,
					}}>
					<Text style={{color: colors.white}}>{groupStatusLabel}</Text>
					</View>
				) : null}

				{this.state.showSearch && (
					<View>
					<View style={{backgroundColor: colors.charcoalLightFade}}>
						<Item
						rounded
						style={{backgroundColor: colors.white, margin: 4, height: 34}}
						key="search">
						<Input
							testID='search-input'
							placeholder="Search"
							style={{padding: 0}}
							value={this.state.searchText}
							onChangeText={searchText => this.setState({searchText})}
						/>
						<TouchableOpacity
							onPress={() =>
							this.setState({
								searchText: '',
								searchFilterOverride: false,
							})
							}
							style={{
								padding: 10,
								alignItems: 'center',
								justifyContent: 'center'
							}}
							activeOpacity={0.7}>
							{this.state.searchText == '' ? (
								<Icon
									name="ios-search"
									style={{color: colors.charcoalLightFade}}
								/>
							) : (
								<FontAwesomeIcon 
									icon={faXmarkCircle} 
									size={20} 
									color={colors.charcoalLightFade}
								/>
							)}
						</TouchableOpacity>
						</Item>
					</View>
					{this.state.searchText != '' && labeledPeople.length == 0 && (
						<View
						style={{
							alignItems: 'center',
							justifyContent: 'center',
							width: '100%',
						}}>
						{activeFilters && !this.state.searchFilterOverride ? (
							<View>
							<Text>No results match your search text and filters</Text>
							<Button
								rounded
								style={{
								alignSelf: 'center',
								backgroundColor: colors.primaryA,
								marginTop: 10,
								}}
								onPress={() => {
								this.setState({searchFilterOverride: true});
								}}>
								<Text
								style={{
									paddingLeft: 15,
									paddingRight: 15,
									color: colors.white,
								}}>
								Search All
								</Text>
							</Button>
							</View>
						) : (
							<Text>No matching results for your search</Text>
						)}
						</View>
					)}
					</View>
				)}

				{isStaffOrAdmin && nameToFacePrompt && (
					<Text
					style={{
						backgroundColor: '#FBF036',
						padding: 10,
						margin: 10,
						borderRadius: 10,
						fontWeight: '500',
						overflow: 'hidden',
					}}>
					This roster has changed-perform a Name to Face
					</Text>
				)}

				{this.state.filterState.selectedViewStyle == 'grid' && (
					<PeopleListGridView
						labeledPeople={labeledPeople}
						onGridPress={item => this._onGridPress(item)}
						isPersonSelected={id => this._isPersonSelected(id)}
						selectMode={this.state.selectMode}
						showScheduledStatus={isStaffOrAdmin}
					/>
				)}

				{this.state.filterState.selectedViewStyle == 'list' && (
					<View testID={"people-list-view"} style={{flexDirection: 'column', marginBottom:120}}>
						<SwipeListView
							useFlatList
							data={labeledPeople}
							style={{top: 0}}
							contentContainerStyle={{paddingBottom: 60}}
							initialNumToRender={25}
							removeClippedSubviews={true}
							windowSize={3}
							disableScrollViewPanResponder={true}
							directionalDistanceChangeThreshold={3}
							keyExtractor={this.listKeyExtractor}
							renderItem={this.listRenderItem}
						/>
					</View>
				)}

				<Modal
					animationType="slide"
					transparent={true}
					visible={this.state.nameToFaceModalVisible}>
					<SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
					<Header style={{backgroundColor: colors.white}}>
						<Left style={{flex: 1}}>
						<Button
							transparent
							onPress={() => this._closeNameToFaceModal()}>
							<Text style={SharedStyles.headerTitleStyle}>Cancel</Text>
						</Button>
						</Left>
						<Body
						style={{
							flex: 1,
							justifyContent: 'center',
							alignItems: 'center',
						}}>
						<Title style={SharedStyles.headerTitleStyle}>
							Name to Face
						</Title>
						</Body>
						<Right style={{flex: 1}}>
						{this.state.savingNameToFace ? (
							<ActivityIndicator
							animating
							size="small"
							color={colors.primaryA}
							/>
						) : (
							<Button
							transparent
							onPress={() => this._saveNameToFace(true)}>
							<Text style={SharedStyles.headerTitleStyle}>Save</Text>
							</Button>
						)}
						</Right>
					</Header>

					{this.state.nameToFacePersons.length == 0 ? (
						<LoadingScreen />
					) : (
						<PeopleListGridView
						labeledPeople={this.state.nameToFacePersons}
						onGridPress={item => this._onGridPress(item)}
						isPersonSelected={id => this._isPersonSelected(id)}
						selectMode={this.state.selectMode}
						/>
					)}
					</SafeAreaView>
				</Modal>

				<Modal
					animationType={'fade'}
					transparent={true}
					visible={this.state.isInCompleteNametoFaceModalVisible}
					onRequestClose={() => {
					this.setState({
						isInCompleteNametoFaceModalVisible: false,
						nameToFaceModalVisible: true,
					});
					}}>
					<View
					style={{
						flex: 1,
						justifyContent: 'center',
						alignItems: 'center',
						backgroundColor: '#E6E7E8',
					}}>
					<View
						style={{
						backgroundColor: colors.white,
						width: '92%',
						borderRadius: 10,
						padding: 20,
						}}>
						<Text
						style={{
							color: colors.black,
							textAlign: 'center',
							fontSize: 18,
						}}>
						Choose a value
						</Text>
						<View style={{marginTop: 20}}>
						<ListItem
							selected={this.state.selectedNametoFaceOption == 'Bathroom'}
							onPress={() =>
							this.setState({selectedNametoFaceOption: 'Bathroom'})
							}>
							<View style={{flexDirection: 'row'}}>
							<Radio
								color={colors.primaryA}
								selectedColor={colors.primaryA}
								selected={
								this.state.selectedNametoFaceOption == 'Bathroom'
								}
								onPress={() =>
								this.setState({selectedNametoFaceOption: 'Bathroom'})
								}
							/>
							<Text
								style={{
								color: colors.black,
								fontSize: 14,
								marginHorizontal: 8,
								}}
								onPress={() =>
								this.setState({selectedNametoFaceOption: 'Bathroom'})
								}>
								Bathroom
							</Text>
							</View>
						</ListItem>
						<ListItem
							selected={this.state.selectedNametoFaceOption == 'Split'}
							onPress={() =>
							this.setState({selectedNametoFaceOption: 'Split'})
							}>
							<View style={{flexDirection: 'row'}}>
							<Radio
								color={colors.primaryA}
								selectedColor={colors.primaryA}
								selected={
								this.state.selectedNametoFaceOption == 'Split'
								}
								onPress={() =>
								this.setState({selectedNametoFaceOption: 'Split'})
								}
							/>
							<Text
								style={{
								color: colors.black,
								fontSize: 14,
								marginHorizontal: 8,
								}}>
								Split
							</Text>
							</View>
						</ListItem>
						<ListItem
							selected={
							this.state.selectedNametoFaceOption == 'Extracurricular'
							}
							onPress={() =>
							this.setState({
								selectedNametoFaceOption: 'Extracurricular',
							})
							}>
							<View style={{flexDirection: 'row'}}>
							<Radio
								color={colors.primaryA}
								selectedColor={colors.primaryA}
								selected={
								this.state.selectedNametoFaceOption ==
								'Extracurricular'
								}
								onPress={() =>
								this.setState({
									selectedNametoFaceOption: 'Extracurricular',
								})
								}
							/>
							<Text
								style={{
								color: colors.black,
								fontSize: 14,
								marginHorizontal: 8,
								}}>
								Extracurricular
							</Text>
							</View>
						</ListItem>
						<ListItem
							selected={this.state.selectedNametoFaceOption == 'Other'}
							onPress={() =>
							this.setState({selectedNametoFaceOption: 'Other'})
							}>
							<View style={{flexDirection: 'row'}}>
							<Radio
								selected={
								this.state.selectedNametoFaceOption == 'Other'
								}
								color={colors.primaryA}
								selectedColor={colors.primaryA}
								onPress={() =>
								this.setState({selectedNametoFaceOption: 'Other'})
								}
							/>
							<Text
								style={{
								color: colors.black,
								fontSize: 14,
								marginHorizontal: 8,
								}}>
								Other
							</Text>
							</View>
						</ListItem>
						</View>
						{this.state.selectedNametoFaceOption == 'Other' && (
						<TextInput
							autoCapitalize="none"
							multiline={true}
							placeholder="Write the reason"
							placeholderTextColor={colors.grayPlatform}
							style={[styles.TextInputStyleClass, {height: 50}]}
							value={this.state.displayReason}
							onChangeText={val => this.setState({displayReason: val})}
						/>
						)}
						{this.state.selectedNametoFaceOptionHasError && (
						<Text
							style={{
							color: colors.red,
							textAlign: 'center',
							fontSize: 15,
							marginTop: 10,
							}}>
							Is necessary to choose one option.
						</Text>
						)}
						<View
						style={{
							flexDirection: 'row',
							justifyContent: 'center',
							marginHorizontal: 10,
							marginTop: 20,
						}}>
						<TouchableOpacity
							style={{
							width: '30%',
							paddingVertical: 5,
							backgroundColor: colors.primaryA,
							marginEnd: 10,
							}}
							onPress={() => this.checkValidationsForNameToFace()}>
							<Text
							style={{
								fontSize: 14,
								color: colors.white,
								textAlign: 'center',
								paddingVertical: 5,
							}}>
							Save
							</Text>
						</TouchableOpacity>
						<TouchableOpacity
							style={{
							width: '30%',
							paddingVertical: 5,
							backgroundColor: colors.primaryA,
							marginStart: 5,
							}}
							onPress={() =>
							this.setState({
								isInCompleteNametoFaceModalVisible: false,
								nameToFaceModalVisible: true,
								selectedNametoFaceOptionHasError: false,
							})
							}>
							<Text
							style={{
								fontSize: 14,
								color: colors.white,
								textAlign: 'center',
								paddingVertical: 5,
							}}>
							Don't save
							</Text>
						</TouchableOpacity>
						</View>
					</View>
					</View>
				</Modal>
				</View>
			</StyleProvider>
    	);
}}

export default withTracker(params => {

	const reservationsHandle = Meteor.subscribe("theReservations", {includeCancellations: true});
	const dayOfWeek = ['sun','mon','tue','wed','thu','fri','sat'];//new moment().format("ddd").toLowerCase();


	const currentOrgs = Orgs.find({_id: Meteor.user() && Meteor.user().orgId})
	const currentOrg = currentOrgs.length > 0 && currentOrgs[0]
	const startOfDay = new moment().startOf('day').valueOf()
	const endOfDay = new moment().endOf('day').valueOf()
	const canceledReservationPeopleIds = Meteor.collection('reservations').find({scheduledDate:{"$gte": startOfDay, "$lt": endOfDay}, cancellationReason:{$exists:true}}, {fields: {selectedPerson: 1}}).map(p => p.selectedPerson)
	const allReservationPeopleIds = Meteor.collection('reservations').find({selectedPerson:{$nin:canceledReservationPeopleIds}, $or:[ {recurringDays:{$in:dayOfWeek}}, { recurringFrequency: {$exists:false} } ]}, {fields: {selectedPerson: 1}}).map( p => p.selectedPerson);

	return {
	  groups: Meteor.collection('groups').find({}, {sort: {name: 1}}),
	  userPerson: Meteor.user() && Meteor.collection('people').findOne({_id: Meteor.user().personId}),
	  recurringReservations: Meteor.collection('reservations').find({$or:[  {recurringDays:{$in:dayOfWeek}}, { recurringFrequency: {$exists:false} } ]}, {fields: {selectedPerson: 1, recurringDays: 1, scheduledDate: 1, scheduledEndDate: 1,scheduleType:1,recurringType:1}}),
		subsLoading: !reservationsHandle.ready(),
		currentOrg,
	};
})(connect()(PeopleList));

function capitalize(s) {
	if (typeof s !== 'string') return ''
  return s.charAt(0).toUpperCase() + s.slice(1)
}
