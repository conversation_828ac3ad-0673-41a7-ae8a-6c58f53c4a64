import React from 'react';
import Meteor from 'react-native-meteor';
import { StyleSheet, Text, View, TouchableOpacity, TouchableHighlight, TextInput, Dimensions, Image, KeyboardAvoidingView, Alert, Platform, Modal, Keyboard, InteractionManager, Linking } from 'react-native';
import NetInfo from "@react-native-community/netinfo";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Spinner } from 'native-base';
import SamlSignInModal from './SamlSignInModal';
import _ from 'lodash';
import Random from '../shared/Random';
import ResetPassword from './ResetPassword';
import Nucleo from './icons/Nucleo';
import colors from '../config/colors.json';
import { connect } from 'react-redux';
import { CommonActions } from '@react-navigation/native';
import CookieManager from '@react-native-cookies/cookies';
import * as environmentSettings from '../shared/Settings';
import Constants from 'expo-constants';
import * as Updates from 'expo-updates';

import {scale, verticalScale, moderateScale} from '../shared/Scaling';
import { clearAppBackground, clearUserData, getUserData, setLoading, userLoginWithPin } from '../actions/userData';
import DeviceInfo from 'react-native-device-info'
import { onSignIn } from './utils/signin.js'

const { width } = Dimensions.get('window');

const getVersionInfo = () => {
	// Option 1: Try manifest first, then expoConfig
	const buildNumber = Platform.select({
		ios: Constants.manifest?.ios?.buildNumber || Constants.expoConfig?.ios?.buildNumber,
		android: Constants.manifest?.android?.versionCode || Constants.expoConfig?.android?.versionCode,
	}) || '0';

	// Option 2: Direct access to native build version
	const nativeBuild = Constants.manifest2?.extra?.expoClient?.buildNumber || 
					   Constants.nativeBuildVersion ||
					   '0';

	return {
		version: Constants.expoConfig?.version || Constants.manifest?.version || '0',
		runtimeVersion: Updates.runtimeVersion || Constants.expoConfig?.runtimeVersion || '0',
		buildNumber: buildNumber !== '0' ? buildNumber : nativeBuild
	};
};

class SignIn extends React.Component {
	constructor(props) {
		super(props);

		this.state = {
			showResetPassword: false,
			cognitoReset: false,
			pin: '',
			supplementalPin: '',
			pinToggle: true,
			entryText: "Use Username and Password",
			email: '',
			password: '',
			error: null,
			samlModalVisible: false,
			credentialToken: null,
			samlProvider: null,
			awaitingLogin: false,
			accounts: [],
			baseServer: environmentSettings.BASE_SERVER,
			showPinCodeDirect: false,
			taps: 0,
		};
	}

	componentDidMount() {
		const { navigation, route } = this.props;
		InteractionManager.runAfterInteractions(async () => {
			const accounts = await Meteor.getAllLocalStorageAccounts();
			const stateObj = {
				accounts
			};

			const useCurrentUserEmail = route?.params?.useCurrentUserEmail ?? false;
			if (useCurrentUserEmail) {
				const user = Meteor.user();
				stateObj.email = user?.emails?.[0]?.address ?? ""
			}
			this.setState(stateObj);
		});
	}

	passwordHelp = () => {
		const { email } = this.state;
		if (email.length === 0) {
			return Alert.alert('Please enter your username first.');
		}

		Meteor.call("mobileCognitoForgotPassword", {username: email}, (error, result) => {
			const newState = { showResetPassword: false };
			if (error) {
				Alert.alert("Error with Forgot Password", error.reason);
			} else if (result) {
				Alert.alert("Please check your email for a verification code");
				newState.cognitoReset = true;
				newState.showResetPassword = true;
			}
			this.setState(newState);
		})
	}

	switchUser = async (user) => {
    const { dispatch } = this.props;
		await dispatch(setLoading(true));
    try {
			await AsyncStorage.removeItem('filterState')
    } catch(e) {
			console.log("error removing filterState (Signin)", e)
			Bugsnag.notify(e);
    }
		try {
			CookieManager.clearAll(true)
			CookieManager.clearAll()
		} catch (error) {
			console.log("error clearing cookies (SignIn)", error)
		}
    await Meteor.accountSwitch(user);
    await dispatch(getUserData(user.token));
		await dispatch(setLoading(false));
  }

	onDismissSaml(result) {
		const { navigation, route } = this.props;
		const navigateOnClose = route?.params?.navigateOnClose ?? false;
		const forceGoBack = route?.params?.forceGoBack ?? false;

		this.setState({samlModalVisible:false});
		if (result.success) {
			setTimeout(() => {
				Meteor.call("rmSamlLogin", { saml: true, credentialToken: result.credentialToken}, (error, result) => {
					this.setState({awaitingLogin:false});
					if(error)
						Alert.alert("Error with SAML login", error.reason);
					else {
						Meteor._loginWithToken(result.token, (error) => {
							if (navigateOnClose) {
								navigation.navigate(navigateOnClose);
							} else if (forceGoBack) {
								navigation.dispatch(state => {
									return CommonActions.reset({
										routes: [
											{ name: 'Home' },
										],
										index: 0,
									});
								});
							}
						});
					}
				});
			}, 6000);

		} else {
			this.setState({awaitingLogin:false});
		}
	}

	isValid() {
		const { email, password } = this.state;
		let valid = false;

		if (email.length > 0 ) {
		  	valid = true;
		}

		if (email.length === 0) {
		  this.setState({ error: 'You must enter a username' });
		}

		return valid;
	}

	toggleEntry = () => {
		const {pinToggle} = this.state;
		let nextEntryText = "Use Username and Password";
		if (pinToggle == true) {
			nextEntryText = "Already logged in? Use your PIN code"
		}
		this.setState({pinToggle: !pinToggle, entryText: nextEntryText});
	}

	onClose = () => {
		const { navigation } = this.props;
		navigation.goBack();
	}

	togglePinDirectLogin = (val) => {
		this.setState({ showPinCodeDirect: val, error: null, pin: '', supplementalPin: '' });
	}

	directPinLogin = () => {
		const { navigation, route, dispatch, personInfo } = this.props;
		const { pin, supplementalPin } = this.state;
		const navigateOnClose = route?.params?.navigateOnClose ?? false;
		const forceGoBack = route?.params?.forceGoBack ?? false;

		if (_.isEmpty(pin)) {
			return this.setState({ error: 'You must enter a PIN' });
		} else  {
			this.setState({ error: null });
		}

		Keyboard.dismiss();
		NetInfo.fetch().then((connectionInfo) => {
			console.log("Meteor connection:", Meteor.status().connected, connectionInfo.type);
			if (!_.includes(["wifi","cellular","unknown"], connectionInfo.type) ) {
				alert("LineLeader requires an internet connection to login. Please check your connection and try again.");
				//alert(connectionInfo.type);
			} else if (!Meteor.status().connected) {
				alert("LineLeader cannot connect to its servers.  Please check your connection and try again");
			} else {
				if (pin.length > 0) {
					dispatch(userLoginWithPin(pin, supplementalPin))
						.then((result) => {
							if (!result) {
								return;
							}
							Meteor._loginWithToken(result, (error) => {
								dispatch(clearAppBackground());
								if (navigateOnClose) {
									navigation.navigate(navigateOnClose);
								} else if (forceGoBack) {
									navigation.dispatch(state => {
										return CommonActions.reset({
											routes: [
												{ name: 'Home' },
											],
											index: 0,
										});
									});
								}
							});
						})
				}
			}
		});
	}

	verifyPin = () => {
		console.log("in verify pin");
		const { navigation, route, dispatch, personInfo } = this.props;
		const navigateOnClose = route?.params?.navigateOnClose ?? false;
		const forceGoBack = route?.params?.forceGoBack ?? false;
		const useCurrentUserEmail = route?.params?.useCurrentUserEmail ?? false;
		Keyboard.dismiss();
		NetInfo.fetch().then((connectionInfo) => {
			if (!_.includes(["wifi","cellular","unknown"], connectionInfo.type) ) {
				alert("LineLeader requires an internet connection to login. Please check your connection and try again.");
				//alert(connectionInfo.type);
			} else if (!Meteor.status().connected) {
				alert("LineLeader cannot connect to its servers.  Please check your connection and try again");
			} else {
				const { pin, accounts, supplementalPin } = this.state;
				const passedUserIds = useCurrentUserEmail ? [] : _.map(accounts, (a) => a.id);
				if (pin.length > 0) {
					this.setState({ awaitingLogin:true });
					Meteor.call("mobilePinCodeValidationWithUserIds", { pin: `${pin}${supplementalPin}`.trim(), userIds: passedUserIds }, (err, result) => {
						if (err) {
							this.setState({ error: err.reason, awaitingLogin: false });
						} else {
							dispatch(clearAppBackground());

							if (result.userData) {
								const account = _.find(accounts, (acc) => acc.id == result.userData._id);

								// local state and server are out-of-sync ... logout
								if (!account) {
									this.setState({awaitingLogin: false});
									Meteor.accountSwitchLogout();
									dispatch(clearUserData());
									return;
								}

								if (result.userData.personId != personInfo._id) {
									console.log("switch for new pin")
									this.setState({awaitingLogin: false});
									return this.switchUser(account)
								}
							}

							if (navigateOnClose) {
								console.log("in navigate on close");
								navigation.navigate(navigateOnClose);
								console.log("after navigate");
							} else if (forceGoBack) {
								console.log("forcegoback");
								navigation.dispatch(state => {
									return CommonActions.reset({
										routes: [
											{ name: 'Home' },
										],
										index: 0,
									});
								});
							}
						}
					})
				} else {
					this.setState({ error: 'You must enter a Pin' });
				}
			}
		});
	}

	saveAndConnect = () => {
		const { baseServer } = this.state;
		const { navigation, route } = this.props;
		Keyboard.dismiss();
		InteractionManager.runAfterInteractions(async () => {
			try {
				await AsyncStorage.setItem('baseServer', baseServer);
			} catch (e) {
				Bugsnag.notify(e);
			}
			environmentSettings.setBaseServer(baseServer);
			Meteor.connect(environmentSettings.WEB_SOCKET_URL);
			navigation.goBack();
		});
	}
	getStatus = () => {
		let status = Meteor.status().connected ? "connected" : "not connected";
		alert(`Metoer status: ${status}`);
	}


	render() {
		const { navigation, route, personInfo, org, pinCodePrompt, loading, dispatch } = this.props;
		const { pinToggle, email, cognitoReset, showPinCodeDirect, taps } = this.state;
		const navigateOnClose = route?.params?.navigateOnClose ?? false;
		let showPinCode = false;
		let showEmailPass = true;
		const showSupplementalPin = route?.params?.showSupplementalPin ?? false;
		const navShowPinCode = route?.params?.showPinCode ?? false;
		if ((navShowPinCode || pinCodePrompt) && org && personInfo) {
			hasCustomization = _.get(org, 'customizations.people/pinCodeCheckin/enabled', false);
			if (hasCustomization) {
				showPinCode = true
				showEmailPass = (!pinToggle) ? true : false;
			}
		}

		if (showPinCodeDirect) {
			return this.renderPinCodeDirectLogin();
		}

		const { version, runtimeVersion, buildNumber } = getVersionInfo();

		return (
      <View style={{flex: 1, backgroundColor: colors.white}}>
        <KeyboardAvoidingView
          behavior={Platform.OS == 'ios' ? 'padding' : null}
          enabled
          style={styles.container}>
          <Modal
            animationType="slide"
            transparent={false}
            visible={this.state.showResetPassword}>
            <ResetPassword
              cognitoReset={cognitoReset}
              email={email}
              onCancel={() => this.setState({showResetPassword: false})}
            />
          </Modal>
          <SamlSignInModal
            visible={this.state.samlModalVisible}
            provider={this.state.samlProvider}
            credentialToken={this.state.credentialToken}
            onDismiss={this.onDismissSaml.bind(this)}
          />
          <TouchableOpacity
						testID='signin-logo'
            onPress={() => this.setState({taps: this.state.taps + 1})}>
            <Image
              style={{marginBottom: 20}}
              source={require('../images/override/login_logo.png')}
            />
          </TouchableOpacity>
          {showPinCode && (
            <TouchableOpacity
              style={{marginBottom: 16}}
              onPress={this.toggleEntry}>
              <Text style={{color: colors.primaryA, fontSize: 18}}>
                {this.state.entryText}
              </Text>
            </TouchableOpacity>
          )}

          {showPinCode && pinToggle && (
            <View style={{alignItems: 'center', marginBottom: 8}}>
              <TextInput
			  				testID='check-in-pin'
                style={styles.input}
                onChangeText={pin => this.setState({pin})}
                value={this.state.pin}
                placeholder="Pin Code"
                placeholderTextColor={colors.grayPlatform}
                autoCapitalize="none"
                autoCorrect={false}
                keyboardType="number-pad"
                secureTextEntry={true}
              />
              {showSupplementalPin && (
                <TextInput
                  style={styles.input}
                  onChangeText={pin => this.setState({supplementalPin: pin})}
                  value={this.state.supplementalPin}
                  placeholder="Supplemental Pin Code"
                  placeholderTextColor={colors.grayPlatform}
                  autoCapitalize="none"
                  autoCorrect={false}
                  keyboardType="number-pad"
                  secureTextEntry={true}
                />
              )}

              <Text style={styles.error}>{this.state.error}</Text>
              {loading || this.state.awaitingLogin ? (
                <Spinner color={colors.primaryA} />
              ) : (
                <TouchableOpacity
                  style={styles.button}
                  onPress={this.verifyPin}>
                  <Text style={styles.buttonText}>Verify Pin</Text>
                </TouchableOpacity>
              )}
            </View>
          )}

          {showEmailPass && (
            <View style={{alignItems: 'center'}}>
              <TextInput
                testID="signin-email"
                style={styles.input}
                onChangeText={email => this.setState({email})}
                value={this.state.email}
                placeholder="Username"
                placeholderTextColor={colors.grayPlatform}
                autoCapitalize="none"
                autoCorrect={false}
                keyboardType="email-address"
              />

              <TextInput
                testID="signin-password"
                style={styles.input}
                onChangeText={password => this.setState({password})}
                value={this.state.password}
                placeholder="Password"
                placeholderTextColor={colors.grayPlatform}
                autoCapitalize="none"
                autoCorrect={false}
                secureTextEntry={true}
              />

              <Text style={styles.error}>{this.state.error}</Text>
              {this.state.awaitingLogin && !this.state.error ? (
                <Spinner testID='signin-spinner' color={colors.primaryA} />
              ) : (
                <TouchableOpacity
                  testID="signin-submit"
                  style={styles.button}
                  onPress={() =>{
										this.setState({error: null, awaitingLogin: true})
                    onSignIn({
											setState: val => this.setState(val),
                      saveAndConnect: this.saveAndConnect,
                      email: this.state.email.trim(),
                      password: this.state.password,
                      dispatch,
                      navigation,
                      route,
                      personInfo,
                      org,
                      pinCodePrompt,
                    })}
                  }>
                  <Text style={styles.buttonText}>Sign In</Text>
                </TouchableOpacity>
              )}

              <TouchableHighlight
                onPress={() => {
                  this.passwordHelp();
                }}>
                <Text style={styles.linkText}>Password help</Text>
              </TouchableHighlight>
            </View>
          )}

          {navigateOnClose && (
            <View style={styles.back}>
              <TouchableOpacity
                onPress={() => this.onClose()}
                style={styles.backButton}>
                <Nucleo name="icon-e-remove" size={24} color={colors.white} />
              </TouchableOpacity>
            </View>
          )}

          {taps > 2 && (
            <View style={{alignItems: 'center', marginVertical: 8}}>
              <TextInput
                testID="signin-customUrl"
                style={styles.input}
                onChangeText={baseServer => this.setState({baseServer})}
                value={this.state.baseServer}
                placeholder="Base URL"
                placeholderTextColor={colors.grayPlatform}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={styles.button}
                onPress={() => this.saveAndConnect()}>
                <Text style={styles.buttonText}>Save and Connect</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.button} onPress={() => this.getStatus()}>
                <Text style={styles.buttonText}>Get Connection Status</Text>
              </TouchableOpacity>
            </View>

          )}
        </KeyboardAvoidingView>
				<View
					style={{
						alignItems: 'center',
						alignSelf: 'center',
						justifyContent: 'center',
						position: 'absolute',
						bottom: 80,
					}}
				>
					{/* <Text>{`Runtime Version: ${runtimeVersion}`}</Text> */}
					<Text>{`Version: ${version}`}</Text>
					<Text>{`Build: ${buildNumber}`}</Text>
				</View>
      </View>
    );
	  }
}

const ELEMENT_WIDTH = width > 350 ? 310 : width - 40;
const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: colors.white,
	},
	input: {
		fontSize: moderateScale(16),
		height: moderateScale(42),
		padding: 4,
		backgroundColor: colors.white,
		borderColor: colors.charcoalLightShade,
		borderWidth: 1,
		width: width > 450 ? 410 : width-40,
		marginBottom: 10,
		color: colors.black,
	},
	button: {
		backgroundColor: colors.primaryA,
		width: ELEMENT_WIDTH,
		paddingVertical: 15,
		alignItems: 'center',
		marginBottom: 30,
	},
	buttonText: {
		color: colors.white,
		fontWeight: '500',
		fontSize: 16,
	},
	linkText: {
		color: colors.primaryA,
		fontWeight: '500',
		fontSize: 14,
	},
	error: {
		color: colors.redBright,
		margin:10
	},
	back: {
		alignItems: 'center',
		marginBottom: 56,
	},
	backButton: {
		alignItems: 'center',
		justifyContent: 'center',
		height: 50,
		width: 50,
		borderRadius: 25,
		backgroundColor: colors.primaryA,
	}
});

const mapStateToProps = (state) => ({
  org: state.auth.orgInfo,
	personInfo: state.auth.personInfo,
	loading: state.auth.loading,
});

export default connect(mapStateToProps)(SignIn);
