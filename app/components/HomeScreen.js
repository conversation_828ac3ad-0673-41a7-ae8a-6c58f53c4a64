import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableWithFeedback,
    TouchableWithoutFeedback,
    TouchableOpacity,
    Button,
    Platform,
    Alert,
    SafeAreaView,
    KeyboardAvoidingView,
} from 'react-native';
import {ActionSheet} from 'native-base';
import PeopleList from './PeopleList';
import SignIn from './SignIn';
import PeopleDetail from './PeopleDetail';
import PeopleScreen from '../screens/People';
import MomentEntry from '../screens/MomentEntry';
import MomentTagger from '../screens/MomentEntry/components/MomentTagger';
import CheckInModal from './CheckInModal';
import CheckOutModal from './CheckOutModal';
import PhotoViewerModal from './PhotoViewerModal';
import FullScreenVideoModal from './FullScreenVideoModal';
import PeopleListSettingsModal from './PeopleListSettingsModal';
import PeopleListSettingsDetailModal from './PeopleListSettingsDetailModal';
import Activity from '../screens/Activity';
import {QRCheckIn} from '../screens/Activity/components';
import Dashboard from '../screens/Dashboard';
import {
  Outlook,
  MomentListModal,
  MomentSuggestionModal,
  AnnouncementsListModal,
  ExpressDriveUpModal,
} from '../screens/Dashboard/components';
import {
  Offline,
  SleepMoments,
  PottyMoment,
  FoodMoment,
  CommentMoment,
} from '../screens/Offline';
import {Tagger} from '../screens/Offline/components';
import DataValidationDetail from '../screens/DataValidationDetail';
import Gallery from '../screens/Gallery';
import Calendar from './Calendar';
import MessageList from './MessageList';
import MessageDetail from './MessageDetail';
import ComposeMessageModal from './ComposeMessageModal';
import GroupListModal from './GroupListModal';
import AccountModal from './AccountModal';
import InvoiceDetailModal from './InvoiceDetailModal';
import PaymentMethodModal from './PaymentMethodModal';
import ModalWebViewCaptive from './ModalWebViewCaptive';
import HelpWebView from './HelpWebView';
import MessageBadgedIcon from './MessageBadgedIcon';
import MessageNotifications from '../api/MessageNotifications';
import ImageScreen from './ImageScreen';

import Meteor, {withTracker} from 'react-native-meteor';
import LoadingScreen from './LoadingScreen';
import {persistNavigationState} from '../helpers/NavState';

import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {NavigationContainer} from '@react-navigation/native';
import {CommonActions} from '@react-navigation/native';
import {connect} from 'react-redux';

import {Icon} from 'native-base';
import Nucleo from './icons/Nucleo';
import colors from '../config/colors.json';
import _ from 'lodash';
import { useFocusAwareSubscriptions } from '../hooks/useFocusAwareSubscription';
import {toggleOfflineMode} from "../actions/userData";
import { accountLongPress } from '../screens/Offline/offlineUtil';
import OfflineBanner from './OfflineBanner';

import ChildrenTagger from '../screens/Offline/components/ChildrenTagger';
import OfflineCheckIn from '../screens/Offline/components/OfflineCheckIn';
import OfflineCheckInModal from '../screens/Offline/components/OfflineCheckInModal';

class CalendarScreen extends React.Component {
  static navigationOptions = ({navigation, route}) => {
    return {
      title: 'Calendar',
    };
  };
  render() {
    return (
      <Calendar
        navigation={this.props.navigation}
        route={this.props.route}></Calendar>
    );
  }
}

class PeopleDetailScreen extends React.Component {
  render() {
    return (
      <PeopleDetail
        navigation={this.props.navigation}
        route={this.props.route}
      />
    );
  }
}

class GalleryScreen extends React.Component {
  static navigationOptions = {
    title: 'Gallery',
  };

  constructor(props) {
    super(props);
    this.state = {
      momentCount: 20,
    };
  }

  onLoadMore = () => {
    this.setState({
      momentCount: this.state.momentCount + 20,
    });
  };

  render() {
    return (
      <Gallery
        momentCount={this.state.momentCount}
        navigation={this.props.navigation}
        onLoadMore={this.onLoadMore}
      />
    );
  }
}

const ActivityStack = createNativeStackNavigator();
function ActivityStackScreen() {
  return (
    <ActivityStack.Navigator
      screenOptions={{
        headerTintColor: colors.primaryA,
        headerTitleAlign: 'center',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}>
      <ActivityStack.Screen
        name="Activity"
        component={Activity}
        options={Activity.navigationOptions}
      />
      <ActivityStack.Screen
        name="DataValidationDetail"
        component={DataValidationDetail}
        options={DataValidationDetail.navigationOptions}
      />
    </ActivityStack.Navigator>
  );
}

const DashboardStack = createNativeStackNavigator();
function DashboardStackScreen() {
  return (
    <DashboardStack.Navigator
      screenOptions={{
        headerTintColor: colors.primaryA,
        headerTitleAlign: 'center',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}>
      <DashboardStack.Screen
        name="Activity"
        component={Dashboard}
        options={Dashboard.navigationOptions}
      />
      <DashboardStack.Screen
        name="DataValidationDetail"
        component={DataValidationDetail}
        options={DataValidationDetail.navigationOptions}
      />
    </DashboardStack.Navigator>
  );
}

const CalendarStack = createNativeStackNavigator();
function CalendarStackScreen() {
  return (
    <CalendarStack.Navigator
      screenOptions={{
        headerTintColor: colors.primaryA,
        headerTitleAlign: 'center',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}>
      <CalendarStack.Screen
        name="Calendar"
        component={CalendarScreen}
        options={CalendarScreen.navigationOptions}
      />
    </CalendarStack.Navigator>
  );
}

const PeopleStack = createNativeStackNavigator();
function PeopleStackScreen() {
  return (
    <PeopleStack.Navigator
      screenOptions={{
        headerTintColor: colors.primaryA,
        headerTitleAlign: 'center',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}>
      <PeopleStack.Screen
        name="People"
        component={PeopleScreen}
        options={PeopleScreen.navigationOptions}
      />
      <PeopleStack.Screen
        name="Person"
        component={PeopleDetailScreen}
        options={{title: 'Profile', headerBackTitle: 'Back'}}
      />
      <PeopleStack.Screen name="Gallery" component={GalleryScreen} />
    </PeopleStack.Navigator>
  );
}

class MessagesScreen extends React.Component {
  static navigationOptions = ({navigation, route}) => {
    return {
      title: 'Messages',
      headerRight: props => {
        return (
          <TouchableWithoutFeedback
            testID="compose-message"
            onPress={() => {
              navigation.navigate('ComposeMessageModal', {
                segment: route.params?.segment,
              });
            }}>
            <Nucleo name="icon-add" size={24} color={colors.primaryA} />
          </TouchableWithoutFeedback>
        );
      },
    };
  };
  render() {
    return (
      <MessageList
        navigation={this.props.navigation}
        route={this.props.route}></MessageList>
    );
  }
}

const MessagesStack = createNativeStackNavigator();
function MessageStackScreen() {
  return (
    <MessagesStack.Navigator
      screenOptions={{
        headerTintColor: colors.primaryA,
        headerTitleAlign: 'center',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}>
      <MessagesStack.Screen
        name="MessageList"
        component={MessagesScreen}
        options={MessagesScreen.navigationOptions}
      />
      <MessagesStack.Screen
        name="MessageDetail"
        component={MessageDetail}
        options={{title: 'Message Detail', headerBackTitle: 'Back'}}
      />
    </MessagesStack.Navigator>
  );
}

function DetailsScreen() {
  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <Text>Details!</Text>
    </View>
  );
}

function HomeScreen2({navigation}) {
  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <Text>Home screen</Text>
      <Button
        title="Go to Details"
        onPress={() => navigation.navigate('Details')}
      />
    </View>
  );
}

const HomeStack = createNativeStackNavigator();

function HomeStackScreen() {
  return (
    <HomeStack.Navigator
      screenOptions={{
        headerTintColor: colors.primaryA,
        headerTitleStyle: {
          fontWeight: 'bold',
          textAlign: 'center',
          alignSelf: 'center',
        },
      }}>
      <HomeStack.Screen name="Home" component={HomeScreen2} />
      <HomeStack.Screen name="Details" component={DetailsScreen} />
    </HomeStack.Navigator>
  );
}

const OfflineStack = createNativeStackNavigator();
function OfflineStackScreen(props) {
	const { dispatch } = props;

	return (
    <NavigationContainer>
      <OfflineStack.Navigator
        screenOptions={{
          headerTintColor: colors.primaryA,
          headerTitleStyle: {
            fontWeight: 'bold',
            textAlign: 'center',
            alignSelf: 'center',
          },
          title: 'Offline Mode',
          headerRight: headerProps => (
            <View style={{flexDirection: 'row'}}>
              <TouchableWithoutFeedback
                onPress={() => accountLongPress({...props})}>
                <Nucleo name="icon-log-in" size={24} color={colors.primaryA} />
              </TouchableWithoutFeedback>
            </View>
          ),
        }}>
        <OfflineStack.Screen
          name="offlineHome"
          component={Offline}
          options={Offline.navigationOptions}
        />
        <OfflineStack.Screen
          name="offlineTagger"
          component={Tagger}
          options={{headerShown: false}}
        />
        <OfflineStack.Screen
          name="childrenTagger"
          component={ChildrenTagger}
          options={{headerShown: false}}
        />
        <OfflineStack.Screen
          name="OfflineCheckInModal"
          component={OfflineCheckInModal}
          options={{headerShown: false}}
        />
        <OfflineStack.Screen
          name="offlineCheckIn"
          component={OfflineCheckIn}
          options={{headerShown: false}}
        />
        <OfflineStack.Screen
          name="offlineMomentEntry"
          component={MomentEntry}
          options={{headerShown: false}}
        />
        <OfflineStack.Screen
          name="offlineSleepMoments"
          component={SleepMoments}
          options={SleepMoments.navigationOptions}
        />
        <OfflineStack.Screen
          name="offlinePottyMoment"
          component={PottyMoment}
          options={PottyMoment.navigationOptions}
        />
        <OfflineStack.Screen
          name="offlineFoodMoment"
          component={FoodMoment}
          options={FoodMoment.navigationOptions}
        />
        <OfflineStack.Screen
          name="offlineCommentMoment"
          component={CommentMoment}
          options={CommentMoment.navigationOptions}
        />
        {/* Checkout functionality is now integrated into ChildrenTagger */}
      </OfflineStack.Navigator>
    </NavigationContainer>
  );
}

const tabBarIconConfiguration = ({route, focused, color, size}) => {
  let iconName;
  if (route.name === 'Activity') {
    iconName = 'icon-home';
  } else if (route.name === 'People') {
    iconName = 'icon-single-02';
  } else if (route.name === 'MomentEntry') {
    const viewContainerStyle = styles.middleButtonContainer;
    const middleButonStyle = styles.middleButton;
    const middleButtomBorderStyle = styles.middleButtonBorderHider;
    const nucleoSize = 24;
    return (
      <View style={viewContainerStyle}>
        <View style={middleButonStyle}>
          <Nucleo
            testID={'moment-create'}
            name="icon-e-add"
            size={nucleoSize}
            color={colors.white}
          />
        </View>
        <View style={middleButtomBorderStyle}></View>
      </View>
    );
  } else if (route.name === 'Calendar') {
    iconName = 'icon-calendar-date-2';
  } else if (route.name === 'Messages') {
    return (
      <MessageBadgedIcon
        style={{color: color}}
        color={color}
        user={Meteor.user()}
      />
    );
  }

  return <Nucleo testID={iconName} name={iconName} size={24} color={color} />;
};

const FamilyTab = createBottomTabNavigator();
const FamilyTabs = () => {
	return (
		<FamilyTab.Navigator
			screenOptions={({ route }) => ({
				tabBarIcon: ({ focused, color, size }) => {
					return tabBarIconConfiguration({ route, focused, color, size });
				},
				tabBarActiveBackgroundColor: colors.primaryA,
				tabBarInactiveBackgroundColor: 'transparent',
				tabBarActiveTintColor: colors.white,
				tabBarInactiveTintColor: colors.grayPlatform,
				tabBarShowLabel: false,
				headerShown: false,
			})}>
			<FamilyTab.Screen name="Activity" component={ActivityStackScreen} />
			<FamilyTab.Screen options={{ tabBarTestID: "people-tab" }} name="People" component={PeopleStackScreen} />
			<FamilyTab.Screen options={{ tabBarTestID: "calendar-tab" }} name="Calendar" component={CalendarStackScreen} />
			<FamilyTab.Screen
				name="Messages"
				component={MessageStackScreen}
				options={{
					tabBarTestID: 'messages-tab',
					unmountOnBlur: true
				}}
				listeners={({ navigation, route }) => ({
					tabPress: e => {
						// Prevent default action
						e.preventDefault();

						const accounts = Meteor.allSavedAccounts();
						if (accounts.length > 1) {
							return navigation.navigate('SignInModal', {
								navigateOnClose: 'Messages',
								useCurrentUserEmail: true,
								showPinCode: true,
								showSupplementalPin: true,
							});
						}
						navigation.navigate('Messages', {screen: 'MessageList'});
						},
						tabLongPress: e => {
							const accounts = Meteor.allSavedAccounts();
							if (accounts.length > 1) {
								return navigation.navigate('SignInModal', {
									navigateOnClose: 'Messages',
									useCurrentUserEmail: true,
									showPinCode: true,
									showSupplementalPin: true,
								});
							}
							navigation.navigate('Messages', {screen: 'MessageList'});
						},
					})}
				/>
			</FamilyTab.Navigator>
		);
};

const StaffTab = createBottomTabNavigator();
const StaffTabs = () => {
	return (
		<StaffTab.Navigator
			screenOptions={({ route }) => ({
				tabBarIcon: ({ focused, color, size }) => { return tabBarIconConfiguration({ route, focused, color, size }) },
				tabBarActiveBackgroundColor: colors.primaryA,
				tabBarInactiveBackgroundColor: 'transparent',
				tabBarActiveTintColor: colors.white,
				tabBarInactiveTintColor: colors.grayPlatform,
				tabBarShowLabel: false,
				headerShown: false
			})}
		>
			<StaffTab.Screen options={{ tabBarTestID: "home-tab" }} name="Activity" component={DashboardStackScreen} />
			<StaffTab.Screen options={{ tabBarTestID: "people-tab" }} name="People" component={PeopleStackScreen} />
			<StaffTab.Screen
				name="MomentEntry"
				component={HomeStackScreen}
				listeners={({ navigation, route }) => ({
					tabPress: (e) => {
						// Prevent default action
						e.preventDefault();
						const state = navigation.getState();
						const userPerson = Meteor.user() && Meteor.collection('people').findOne({ _id: Meteor.user().personId })
						if (Orgs.current() && Orgs.current().hasCustomization("moments/staffRequiredCheckin/enabled") && userPerson && !userPerson.checkedIn) {
							Alert.alert("Error", "Staff and admins must be checked in to post moments. Please visit your profile to check in.");
						} else {
							navigation.navigate('MomentEntryModal', { newMomentType: 'other', tabIndex: state.index })
						}
					},
					tabLongPress: (e) => {
						const state = navigation.getState();
						const userPerson = Meteor.user() && Meteor.collection('people').findOne({ _id: Meteor.user().personId })
						if (Orgs.current() && Orgs.current().hasCustomization("moments/staffRequiredCheckin/enabled") && userPerson && !userPerson.checkedIn) {
							Alert.alert("Error", "Staff and admins must be checked in to post moments. Please visit your profile to check in.");
						} else {
							navigation.navigate('MomentEntryModal', { newMomentType: 'other', tabIndex: state.index })
						}
					}
				})}
			/>

			<StaffTab.Screen options={{ tabBarTestID: "calendar-tab" }} name="Calendar" component={CalendarStackScreen} />
			<StaffTab.Screen
				name="Messages"
				component={MessageStackScreen}
				options={{
					tabBarTestID: 'messages-tab',
					unmountOnBlur: true
				}}
				listeners={({ navigation, route }) => ({
					tabPress: (e) => {
						// Prevent default action
						e.preventDefault();

						const accounts = Meteor.allSavedAccounts();
						if (accounts.length > 1) {
							return navigation.navigate('SignInModal', {
								navigateOnClose: 'Messages',
								useCurrentUserEmail: true,
								showPinCode: true,
								showSupplementalPin: true,
							});
						}
						navigation.navigate('Messages', {screen: 'MessageList'});
					},
					tabLongPress: e => {
						// Prevent default action
						const accounts = Meteor.allSavedAccounts();
						if (accounts.length > 1) {
							return navigation.navigate('SignInModal', {
								navigateOnClose: 'Messages',
								useCurrentUserEmail: true,
								showPinCode: true,
								showSupplementalPin: true,
							});
						}
						navigation.navigate('Messages', {screen: 'MessageList'});
					},
				})}
			/>
		</StaffTab.Navigator>
	);
};

const HomeStackNav = createNativeStackNavigator();
function ModalStackScreens() {
  return (
    <HomeStackNav.Group
      screenOptions={{
        presentation: 'modal',
        headerShown: false,
        headerTitleAlign: 'center',
      }}>
      <HomeStackNav.Screen
        name="ExpressDriveUpModal"
        component={ExpressDriveUpModal}
      />
      <HomeStackNav.Screen
        name="AnnouncementsListModal"
        component={AnnouncementsListModal}
      />
      <HomeStackNav.Screen
        name="MomentSuggestionModal"
        component={MomentSuggestionModal}
      />
      <HomeStackNav.Screen name="MomentListModal" component={MomentListModal} />
      <HomeStackNav.Screen name="QRCheckInModal" component={QRCheckIn} />
      <HomeStackNav.Screen name="OutlookModal" component={Outlook} />
      <HomeStackNav.Screen name="SignInModal" component={SignIn} />
      <HomeStackNav.Screen name="GroupListModal" component={GroupListModal} />
      <HomeStackNav.Screen
        name="PeopleListSettingsModal"
        component={PeopleListSettingsModal}
      />
      <HomeStackNav.Screen
        name="PeopleListSettingsDetailModal"
        component={PeopleListSettingsDetailModal}
      />
      <HomeStackNav.Screen name="CheckInModal" component={CheckInModal} />
      <HomeStackNav.Screen name="CheckOutModal" component={CheckOutModal} />
      <HomeStackNav.Screen
        name="HelpWebView"
        component={HelpWebView}
        options={HelpWebView.navigationOptions}
      />
      <HomeStackNav.Screen name="MomentEntryModal" component={MomentEntry} />
      <HomeStackNav.Screen
        name="MomentEntryMomentTagger"
        component={MomentTagger}
      />
      <HomeStackNav.Screen
        name="PhotoViewerModal"
        component={PhotoViewerModal}
        options={{
          presentation: 'card',
          headerShown: true,
          title: 'Photos',
          headerTintColor: colors.primaryA,
        }}
      />
      <HomeStackNav.Screen
        name="FullScreenVideoModal"
        component={FullScreenVideoModal}
      />
      <HomeStackNav.Screen
        name="ComposeMessageModal"
        component={ComposeMessageModal}
      />
      <HomeStackNav.Screen name="AccountModal" component={AccountModal} />
      <HomeStackNav.Screen
        name="InvoiceDetailModal"
        component={InvoiceDetailModal}
      />
      <HomeStackNav.Screen
        name="PaymentMethodModal"
        component={PaymentMethodModal}
      />
      <HomeStackNav.Screen name="ImageScreen" component={ImageScreen} />
    </HomeStackNav.Group>
  );
}

const LockedScreensNav = createNativeStackNavigator();
function LockedScreens() {
  return (
    <LockedScreensNav.Group
      screenOptions={{headerShown: false, gestureEnabled: false}}>
      <LockedScreensNav.Screen
        name="ModalWebViewCaptive"
        component={ModalWebViewCaptive}
        options={{headerShown: false, gestureEnabled: false}}
      />
    </LockedScreensNav.Group>
  );
}

function staffStackContainer() {
	return (
		<NavigationContainer>
			<HomeStackNav.Navigator>
				<HomeStackNav.Screen
					name="Home"
					component={StaffTabs}
					options={{ headerShown: false }}
				/>
				{ModalStackScreens()}
				{LockedScreens()}
			</HomeStackNav.Navigator>
		</NavigationContainer>
	)
}

function familyStackContainer() {
	return (
		<NavigationContainer>
			<HomeStackNav.Navigator>
				<HomeStackNav.Screen
					name="Home"
					component={FamilyTabs}
					options={{ headerShown: false }}
				/>
				{ModalStackScreens()}
				{LockedScreens()}
			</HomeStackNav.Navigator>
		</NavigationContainer>
	)
}

const styles = {
  middleButton: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    width: 50,
    borderRadius: 25,
    backgroundColor: colors.primaryA,
    zIndex: 99999,
    elevation: 99999,
  },
  middleButtonHorizontal: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 30,
    width: 30,
    borderRadius: 15,
    backgroundColor: colors.primaryA,
    zIndex: 99999,
    elevation: 99999,
  },
  middleButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 72,
    width: 72,
    borderRadius: 36,
    marginBottom: 25,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: colors.bottomTabBorder,
    backgroundColor: colors.white,
  },
  middleButtonContainerHorizontal: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 52,
    width: 52,
    borderRadius: 26,
    marginBottom: 12.5,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: colors.bottomTabBorder,
    backgroundColor: colors.white,
  },
  middleButtonBorderHider: {
    position: 'absolute',
    backgroundColor: colors.white,
    width: 72,
    height: 48.5,
    top: 23.5,
  },
  middleButtonBorderHiderHorizontal: {
    position: 'absolute',
    backgroundColor: colors.white,
    width: 52,
    height: 48.5,
    top: 17.5,
  },
};

class HomeScreen extends Component {

	constructor(props) {
		super(props);
		this.state = {
			userType: null,
		}

		this.peopleHandle = null;
		this.announcementsHandle = null;
		this.invoicesHandle = null;
	}

	componentDidMount() {
		console.log("HomeScreen mounted");


		if (this.peopleHandle) this.peopleHandle.stop();
		this.peopleHandle = Meteor.subscribe("theMobilePeople")

		if (this.announcementsHandle) this.announcementsHandle.stop();
		this.announcementsHandle = Meteor.subscribe("theAnnouncements");

		if (this.invoicesHandle) this.invoicesHandle.stop();
		this.invoicesHandle = Meteor.subscribe("theInvoices");
	}

	shouldComponentUpdate(nextProps, nextState) {
		const currentUserPersonType = _.get(this.props, 'userPerson.type', null);
		const nextUserPersonType = _.get(nextProps, 'userPerson.type', null);
		const currentUserPersonId = _.get(this.props, 'userPerson._id', null);
		const nextUserPersonId = _.get(nextProps, 'userPerson._id', null);
		const currentMode = _.get(this.props, 'offlineMode', false);
		const nextMode = _.get(nextProps, 'offlineMode', false);

		if ((currentUserPersonType != nextUserPersonType) || (currentUserPersonId != nextUserPersonId) || (currentMode != nextMode)) {
			//TODO: MAYBE need to redo subs here on person change???
			return true;
		}
		return false;
	}

	componentWillUnmount() {
		if (this.peopleHandle) this.peopleHandle.stop();
		if (this.announcementsHandle) this.announcementsHandle.stop();
		if (this.invoicesHandle) this.invoicesHandle.stop();
	}

	render() {
		const userType = _.get(this.props.userPerson, 'type', null);
		const { dispatch, offlineMode } = this.props;

		return (
			<SafeAreaView
				style={{
					height: '100%'
				}}
			>
					{['staff', 'admin'].includes(userType)
						? (offlineMode
							? OfflineStackScreen({offlineMode, dispatch})
							: staffStackContainer())
						: familyStackContainer()
					}
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}

    >
				  <OfflineBanner/>
    </KeyboardAvoidingView>
			</SafeAreaView>
		);
	}
}
const mapStateToProps = (state) => ({
  offlineMode: state.auth.offlineMode,
});

export default connect(mapStateToProps)(HomeScreen);
