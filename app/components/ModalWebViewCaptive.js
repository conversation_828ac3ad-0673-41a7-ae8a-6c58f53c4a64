import React from 'react';
import Meteor from 'react-native-meteor';
import {
  StyleSheet,
  View,
  BackHandler,
  ActivityIndicator,
  Dimensions,
  Alert,
  Platform,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  Container,
  Header,
  Content,
  Body,
  Title,
  Text,
  StyleProvider,
  Left,
  Right,
  Button,
  Icon,
} from 'native-base';
import {WebView} from 'react-native-webview';
import LoadingScreen from './LoadingScreen';
import SharedStyles from '../shared/SharedStyles';
import getTheme from '../../native-base-theme/components';
import commonColor from '../../native-base-theme/variables/commonColor';
import AsyncStorage from '@react-native-async-storage/async-storage';
import colors from '../config/colors.json';
import {CommonActions} from '@react-navigation/native';
import Nucleo from '../components/icons/Nucleo';

import * as environmentSettings from '../shared/Settings';

const width = Dimensions.get('window').width,
  height = Dimensions.get('window').height;

class ModalWebViewCaptive extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      authToken: null,
      loading: true,
      isBackArrowVisible: true,
      key: 1,
    };
    this.webview = null;
    if (!(this.props.route && this.props.route?.params?.location))
      this.props.navigation.dispatch(CommonActions.goBack());
  }

  componentDidMount() {
    this._getToken()
    this.backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      return true;
    });
  }

  componentWillUnmount() {
    try {
      this.backHandler.remove();
    } catch (e) {
      console.log(e, 'error with unmount remove');
    }
  }

  async _getToken() {
    try {
      const token = await AsyncStorage.getItem('reactnativemeteor_usertoken');
      console.log('Token retrieved:', token);
      if (token !== null) this.setState({authToken: token});
    } catch (err) {
      console.log('Error retrieiving token:', err);
      this.props.navigation.dispatch(CommonActions.goBack());
    }
  }

  onCloseModal() {
    this.props.navigation.dispatch(CommonActions.goBack());
  }

  onSignout() {
    Meteor.logout();
    this.props.navigation.getState().pop();
  }

  render() {
    if (!this.state.authToken) return <LoadingScreen />;

    const location = this.props.route?.params?.location;

    let title, url, injectedJavaScript;

    if (location == 'pinCode') {
      title = 'PIN Code Check-in';
      url =
        environmentSettings.APP_URL +
        '/loginWithRedirect?dest=/kiosk/pinCodeCheckin&t=' +
        this.state.authToken;
    } else if (location == 'help') {
      title = 'Help';
      url =
        environmentSettings.APP_URL +
        '/loginWithRedirect?dest=/helpRedirect&t=' +
        this.state.authToken;
    } else if (location == 'qrCheckIn') {
      const qrdata = this.props.route?.params?.qrdata;
      injectedJavaScript = `MpData = {}; MpData.qrdata = "${qrdata}";`;
      title = 'QR Check-in';
      url =
        environmentSettings.APP_URL +
        '/loginWithRedirect?dest=/kiosk/pinCodeCheckin?qr=true&qr=true&t=' +
        this.state.authToken;
    }
    return (
      <StyleProvider style={getTheme(commonColor)}>
        <Container>
          <Header style={{backgroundColor: colors.white}}>
            <Left style={{flex: 1}}>
              {this.state.isBackArrowVisible && (
                <Button
                  transparent
                  onPress={() => {
                    this.props.navigation.dispatch(CommonActions.goBack());
                  }}>
                  {Platform.OS === 'ios' ? (
                    <View style={{flexDirection: 'row'}}>
                      <Nucleo
                        name="icon-left-arrow"
                        size={20}
                        color={colors.primaryA}
                      />
                      <Text style={{color: colors.primaryA}}>Back</Text>
                    </View>
                  ) : (
                    <Icon
                      name="arrow-back"
                      style={{color: colors.primaryA}}></Icon>
                  )}
                </Button>
              )}
            </Left>

            <Body
              style={{flex: 2, justifyContent: 'center', alignItems: 'center'}}>
              <Title style={SharedStyles.headerTitleStyle}>{title}</Title>
            </Body>
            <Right style={{flex: 1}}>
              {location == 'help' && (
                <Button transparent onPress={() => this.onCloseModal()}>
                  <Text style={SharedStyles.headerTitleStyle}>Done</Text>
                </Button>
              )}
            </Right>
          </Header>
          <WebView
            key={this.state.key}
            ref={ref => (this.webview = ref)}
            useWebKit={true}
            source={{uri: url}}
            onLoad={() => this.setState({loading: false})}
            onMessage={event => {
              const message = event.nativeEvent.data;
              if (message == 'close') {
                this.props.navigation.dispatch(CommonActions.goBack());
              } else if (message == 'kioskPinEntered') {
                this.setState({isBackArrowVisible: false});
              }
            }}
            thirdPartyCookiesEnabled={true}
            sharedCookiesEnabled={true}
            originWhitelist={['*']}
            javaScriptEnabled
            javaScriptEnabledAndroid
            injectedJavaScript={injectedJavaScript}
            onNavigationStateChange={async navState => {
              if (
                navState.url.includes('from=manage') &&
                this.webview != null
              ) {
                this._getToken().then(() => {
                  this.setState({key: this.state.key + 1}); // Force the Webview to reload
                });
              }
            }}
          />
          {this.state.loading && (
            <ActivityIndicator
              style={{
                position: 'absolute',
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
                alignItems: 'center',
                justifyContent: 'center',
              }}
              size="large"
            />
          )}
        </Container>
      </StyleProvider>
    );
  }
}

export default ModalWebViewCaptive;
