import React from 'react';
import Meteor from 'react-native-meteor';
import { ActivityIndicator, StyleSheet, View} from 'react-native';
import {Button, Text} from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { connect } from 'react-redux';
import { clearUserData } from '../actions/userData';

import colors from '../config/colors.json';

class LoadingScreen extends React.Component {
	constructor(props) {
		super(props);
		this.state = { showSignOut: false };
	}

	componentDidMount() {
		setTimeout(() => {this.setState({showSignOut: true})}, 5000);
	}

	async onSignout() {
		const { dispatch } = this.props;
		if (this.props.onSignout)
			this.props.onSignout();
		Meteor.logout();
		await this._resetToken()
		dispatch(clearUserData());
	}

	async _resetToken() {
		try {
			await AsyncStorage.removeItem('reactnativemeteor_usertoken');
		} catch (err) {
			console.log("Error resetting token:", err);
		}
	}

	render() {
	  const {message} = this.props;
	  return (
		<View style={[styles.container, styles.horizontal]}>
			<View style={{flexDirection:"column"}}>
				<ActivityIndicator testID={'loading-icon'} size="large" color={colors.primaryA} />
				{message ? <Text>{message}</Text> : null }
				{this.state.showSignOut ? <Button style={{backgroundColor: colors.primaryA, marginTop:15}} onPress={() => this.onSignout()}>
					<Text>Sign Out</Text>
				</Button> : null}
			</View>
		</View>
	  )
	}
}


const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
	    alignItems: 'center',
	},
	horizontal: {
		flexDirection: 'row',
		justifyContent: 'space-around',
		padding: 10
	}
});

export default connect()(LoadingScreen);
