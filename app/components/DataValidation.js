import React from 'react'
import Meteor from 'react-native-meteor';
import { View, Text, TouchableOpacity, Dimensions, Image } from 'react-native';
import Nucleo from './icons/Nucleo';
import colors from '../config/colors.json';

import styles from '../screens/Dashboard/components/styles';
import _ from 'lodash';

const photoWidth = Dimensions.get('window').width - 80;
const localStyle = {
  image: {
    width: photoWidth,
    height: photoWidth,
    borderRadius: 16,
    marginBottom: 16,
  },
}


class DataValidation extends React.Component {
  
  constructor(props) {
    super(props);
    this.state = {
      dataValidations: [],
    };
  }
  
  _getValidations = () => {
    Meteor.call("getDataValidations", (error, res) => {
      if (error) {
        console.log(error);
      }
      
      if (res && res.dataValidations) {
        this.setState({
          dataValidations: res.dataValidations,
        });
      }
    });
  }
  
  componentDidMount() {
    this._getValidations();
  }
  
  UNSAFE_componentWillReceiveProps(props) {
    const { refreshRequestAt } = this.props;
    if (props.refreshRequestAt !== refreshRequestAt) {
      this._getValidations();
    }
  }
  
  render () {
    const { dataValidations } = this.state;
    const { onDataValidationPress } = this.props;
    if (_.isEmpty(dataValidations)) return null;
    
    return (
      <View style={{flex:1,flexDirection:'column'}}>
        {
          dataValidations.map((dv) => (
            <View style={styles.card}>
              <View style={styles.headerLabelView}>
                <Nucleo name="icon-megaphone" size={16} color={colors.primaryA} style={{marginRight:8}}/>
                <Text style={{fontWeight: 'bold', color: colors.primaryA}}>Is your information up-to-date?</Text>
              </View>
              {dv.imageUrl ?
                <Image
                  style={localStyle.image}
                  source={{uri: dv.imageUrl}}
                /> : null
              }
              <Text style={{color: colors.black, fontSize: 16, marginBottom: 24, lineHeight: 25}}>{dv.description}</Text>
              <TouchableOpacity onPress={() => onDataValidationPress(dv)} style={styles.button}>
                <Text style={styles.buttonText}>Verify Information</Text>
              </TouchableOpacity>
            </View>
          ))
        }
      </View>
    )
  }
}

export default DataValidation;
