import AWS from 'aws-sdk';
import DeviceInfo from 'react-native-device-info';
import Meteor from 'react-native-meteor';
import {store} from '../store';
import * as environmentConfig from '../config/constants.json';

const baseUrl = environmentConfig.BASE_SERVER;
const uniqueId = DeviceInfo.getUniqueId();
const appVersion = DeviceInfo.getVersion();
const buildNumber = DeviceInfo.getBuildNumber();
const platform = DeviceInfo.getSystemName();
const appName = DeviceInfo.getApplicationName();
const userId = Meteor.user()?._id;
const logGroupName = 'Engage';
let logStreamName =
  baseUrl.includes('staging') || baseUrl.includes('localhost')
    ? 'Staging'
    : 'Production';
const credentials = {
  accessKeyId: process.env['AWS_ACCESS_KEY_ID'],
  secretAccessKey: process.env['AWS_SECRET_KEY'],
};
console.log(process.env['AWS_ACCESS_KEY_ID'], process.env['AWS_SECRET_KEY']);
const cloudWatchLogs = new AWS.CloudWatchLogs({
  region: 'us-east-1',
  credentials,
});
export const initializeCloudwatch = async () => {
  try {
    await cloudWatchLogs
      .createLogStream({
        logGroupName,
        logStreamName,
      })
      .promise()
      .catch(error => {
        if (error.code.includes('ResourceAlreadyExistsException')) {
        } else {
          console.error('Error creating log stream:', error);
        }
      });
  } catch (error) {
    console.error('Error creating log stream:', error);
  }
};
export const sendToCloudWatch = async (level, ...args) => {
  try {
    const rawMessage = args
      .map(arg => (typeof arg === 'object' ? JSON.stringify(arg) : String(arg)))
      .join(' ');
    const logData = {
      level: level,
      message: rawMessage,
      metadata: {
        appName,
        appVersion,
        buildNumber,
        platform,
        baseUrl,
        userId,
        uniqueId,
      },
    };
    const params = {
      logGroupName,
      logStreamName,
      logEvents: [
        {
          message: JSON.stringify(logData),
          timestamp: Date.now(),
        },
      ],
    };

    await cloudWatchLogs.putLogEvents(params).promise();
  } catch (error) {
    return
  }
};

const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleDebug = console.debug;

// console.log = async (...args) => {
//   originalConsoleLog.apply(console, args);
//   await sendToCloudWatch('INFO', ...args);
// };
//
// console.error = async (...args) => {
//   originalConsoleError.apply(console, args);
//   await sendToCloudWatch('ERROR', ...args);
// };
//
// console.warn = async (...args) => {
//   originalConsoleWarn.apply(console, args);
//   await sendToCloudWatch('WARN', ...args);
// };
//
// console.debug = async (...args) => {
//   originalConsoleDebug.apply(console, args);
//   await sendToCloudWatch('DEBUG', ...args);
// };
