import React from 'react';
import { Platform, View, Text } from 'react-native';
import Meteor from 'react-native-meteor';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Header, Content, Body, Title, Left, Right, Item, Input, Label, Textarea, Icon, <PERSON><PERSON><PERSON><PERSON>, Spinner, Picker} from 'native-base';
import moment from 'moment';
import DateTimePicker from 'react-native-modal-datetime-picker';
import SharedStyles from '../shared/SharedStyles';
import getTheme from '../../native-base-theme/components';
import commonColor from '../../native-base-theme/variables/commonColor';
import colors from '../config/colors.json';
import { CommonActions } from '@react-navigation/native';
import Relationships from '../api/Relationships';
import People from '../api/People';
import Orgs from '../api/Orgs';
const moodDefs = [
	{fieldValue: "Happy", fieldIcon: "smile-o"},
	{fieldValue: "SoSo", fieldIcon: "meh-o"},
	{fieldValue: "Sad", fieldIcon: "frown-o"}
];

class CheckOutModal extends React.Component {


	constructor(props) {
		super(props);

		const currentPerson = this.props.route?.params?.person;
		const relationships = Relationships.find({targetId: currentPerson._id});
		const relatedPeople = relationships.map((r) => People.findOne(r.personId));
		this.state = {
			showTimePicker: false,
			time: new Date(),
			comments: "",
			checkoutMood: null,
			isSaving: false,
			selectedPickedUpById: null,
			healthCheckSelections: {},
			currentOrg: Orgs.findOne({_id: currentPerson.orgId}),
			relatedPeople,
		};
	}

	onChangeValue = (value) => {
		this.setState({selectedPickedUpById: value})
	}

	onSave() {
		this.setState({isSaving: true});
		const person = this.props.route?.params?.person;
		const checkOutData = {
			personId: person._id,
			prettyTime: moment(this.state.time).format("h:mm a"),
			comments: this.state.comments,
			mood: this.state.checkoutMood,
			checkedOutById: this.state.selectedPickedUpById
		};
		if (this.hasHealthCheck()) {
			checkOutData.healthChecks = this.state.healthCheckSelections
		}

		Meteor.call("checkOut", checkOutData, (error, result) => {
			this.setState({isSaving: false});
			if (error)
				alert(error.reason);
			else {
				const p = Meteor.collection('people').findOne(checkOutData.personId);
				if (p) {
					p.checkedIn = false;
					p.checkInGroupId = null;
					p.checkInGroupName = null;
					Meteor.collection('people').saveLocal(p);
				};
				this.props.navigation.goBack();
				this.props.route?.params?.rerender?.()
				this.props.route?.params?.getRatios?.()
			}
		});

	}

	hasHealthCheck = () => {
		const { currentOrg } = this.state;
		return currentOrg && currentOrg.hasCustomization("moments/checkin/showHealthCheck");
	}

	getAvailableHealthCheckTypes = () => {
		const { currentOrg } = this.state;
		return currentOrg ? currentOrg.availableCheckInHealthCheckTypes() : [];
	}

	healthCheckSelection = (i, value) => {
		let currentHealthCheckSelections = this.state.healthCheckSelections;
		currentHealthCheckSelections[i] = value;
		this.setState({ healthCheckSelections: currentHealthCheckSelections });
	}

	render() {
		const person = this.props.route?.params?.person;
    const relatedPeople = this.props.route?.params?.relatedPeople
      ? [
          ...this.props.route?.params?.relatedPeople,
          {
            person: {
              firstName: 'None',
              lastName: '',
              _id: null,
            },
          },
        ]
      : [{person: {firstName: 'None', lastName: '', _id: null}
	}];
		let showHealthCheck = false;
		let availableHealthCheckTypes = [];

		if (this.hasHealthCheck()) {
			showHealthCheck = true;
			availableHealthCheckTypes = this.getAvailableHealthCheckTypes();
		}

		return (
			<StyleProvider style={getTheme(commonColor)}>
			<Container>
				<Header style={{backgroundColor: colors.white}}>
					<Left style={{flex:1}}>
						<Button transparent onPress={() => this.props.navigation.dispatch(CommonActions.goBack()) }>
							{ Platform.OS === 'ios' ? (
							<Text style={SharedStyles.headerTitleStyle}>Cancel</Text>
							) : (
							<Icon name='arrow-back-circle' style={{color: colors.primaryA}}></Icon>
							)}
						</Button>
					</Left>
					<Body style={{flex:1,justifyContent:'center',alignItems:'center'}}>
						<Title style={SharedStyles.headerTitleStyle}>Check Out</Title>
					</Body>
					<Right style={{flex:1}}>
						{this.state.isSaving ? (
							<Spinner color={colors.primaryA}/>
						) : (
						<Button transparent onPress={() => this.onSave()}>
							<Text style={SharedStyles.headerTitleStyle}>Save</Text>
						</Button>
						)}
					</Right>
				</Header>
				<Content>


					<Item stackedLabel disabled>
						<Label>Person</Label>
            			<Input disabled>{person.firstName} {person.lastName}</Input>
					</Item>
					<Item stackedLabel style={{flexDirection: 'column', alignItems: 'flex-start'}}>
						<Label>Time</Label>
						<Button transparent onPress={()=> {this.setState({showTimePicker: !this.state.showTimePicker})}}>
							<Text style={{paddingLeft:5}}>{moment(this.state.time).format("h:mm a")}</Text>
						</Button>
						<DateTimePicker
							isVisible={this.state.showTimePicker}
							onConfirm={(datetime)=>(this.setState({time:datetime, showTimePicker: false}))}
							onCancel={()=> {this.setState({showTimePicker: !this.state.showTimePicker})}}
							mode="time"
						/>
					</Item>
					<Item stackedLabel style={{flexDirection: 'column', alignItems: 'flex-start'}}>
						<Label>Mood</Label>
						<View style={{flex:1, flexDirection:"row", margin:5}}>
						{moodDefs.map( (mood) => {
							return(<Button
								bordered={this.state.checkoutMood != mood.fieldValue}
								onPress={() => {this.setState({checkoutMood: mood.fieldValue})}}
								style={{marginRight:5}}>
									<Icon type="FontAwesome" name={mood.fieldIcon}/>
								</Button>)
						})}
						</View>
					</Item>
					<Item stackedLabel disabled style={{flexDirection: 'column', alignItems: 'flex-start'}}>
						<Label>Picked Up By</Label>
						<Picker
							testID="picked-up-picker"
							style={{marginLeft:0, paddingLeft:0, width:"100%"}}
							mode="dropdown"
							placeholder="Choose person"
							placeholderStyle={{ color: colors.primaryA }}
							textStyle={{ color: colors.primaryA }}
							placeholderIconColor={colors.blue}
							selectedValue={this.state.selectedPickedUpById}
							onValueChange={this.onChangeValue.bind(this)}
							headerBackButtonTextStyle={{ color: colors.primaryA }}
            >
								{relatedPeople.map( (relatedPerson) => {
									return <Picker.Item label={relatedPerson.person.firstName + " " + relatedPerson.person.lastName} value={relatedPerson.person._id} style={{color: colors.black,backgroundColor: colors.white}}/>
								})}
						</Picker>
					</Item>
					{
						showHealthCheck ? (
							<View style={{flex:1, flexDirection: 'column'}}>
								<Item stackedLabel>
									<Label>Health Check</Label>
										{ availableHealthCheckTypes.map((i, index) => {
											return (
												<View style={{flex:1, flexDirection: "row", alignItems:'center', justifyContent: "space-between", marginBottom: 8}}>
													<View style={{flexDirection: "row", width: "40%", justifyContent:'flex-end', marginRight: 8}}>
														<Text style={{color: `${colors.black}80`, fontWeight: 'bold', fontSize: 16}}>{i}</Text>
													</View>
													<View style={{flex:1, flexDirection: "row"}}>
														<Button
															style={{backgroundColor: this.state.healthCheckSelections[i] == "good" ? colors.primaryA : colors.white,
																	borderColor: colors.primaryA, borderRadius:4, borderWidth:0.5}}
															onPress={()=> this.healthCheckSelection(i, "good")}>
															<Icon style={{color: this.state.healthCheckSelections[i] == "good" ? colors.white : colors.primaryA}} name='happy-outline'  />
														</Button>
														<Button
															style={{backgroundColor: this.state.healthCheckSelections[i]  == "bad" ? colors.primaryA : colors.white,
																	borderColor: colors.primaryA, borderRadius:4, borderWidth:0.5, marginLeft:5}}
														 onPress={()=> this.healthCheckSelection(i, "bad") }>
															<Icon style={{color: this.state.healthCheckSelections[i] == "bad" ? colors.white : colors.primaryA}} name='sad-outline' />
														</Button>
													</View>
												</View>
											)
										}) }
								</Item>
							</View>
						) : null
					}
					<Item stackedLabel>
						<Label>Comment</Label>
						<Textarea testID={'comment-input'} onChangeText={(comments) => this.setState({comments})}
							rowSpan={5} style={{width:"100%"}} placeholder="Enter any comments..." />
					</Item>

				</Content>
			</Container>
			</StyleProvider>
		)
	}
}

export default CheckOutModal;
