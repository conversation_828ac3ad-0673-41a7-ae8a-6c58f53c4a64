import React, {useState, useEffect, useCallback} from 'react';
import {
  TouchableWithoutFeedback,
  View,
  TouchableOpacity,
  Text,
  Alert,
  FlatList,
} from 'react-native';
import Meteor from 'react-native-meteor';

import {useDispatch, useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import Nucleo from '../../components/icons/Nucleo';
import colors from '../../config/colors.json';

import {toggleOnlineMode} from '../../actions/userData';
import {syncMoments, sleepCheck} from '../../actions/offline';
import Orgs from '../../api/Orgs';
import {getCachedPeople} from './cacheHelpers';
import {
  getPendingCheckInsCount,
} from './syncHelpers';
import {
  onCheckIn,
  onCheckOut,
  onNameToFace,
  onSleepMoment,
  onPottyMoment,
  onFoodMoment,
  onCommentMoment,
} from './offlineHelpers';
import {OFFLINE_CLEAR_MOMENTS} from '../../utility/actions';
import AsyncStorage from '@react-native-async-storage/async-storage';

const localStyles = {
  button: {
    backgroundColor: colors.primaryA,
    borderRadius: 10,
    paddingVertical: 12,
    marginTop: 24,
    marginHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  buttonContainer: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    marginHorizontal: 16,
    marginVertical: 8,
  },
};

const Offline = props => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const cachedPeople = useSelector(state => state.auth.offlinePeople);
  const {momentsToSync, currentPerson, currentOrg} = useSelector(state => ({
    momentsToSync: state.offline.momentsToSync,
    momentsSyncing: state.offline.momentsSyncing,
    offlinePeople: state.auth.offlinePeople,
    userInfo: state.auth.userInfo,
    currentPerson: state.auth.personInfo,
    currentOrg: state.auth.orgInfo,
  }));

  // Store the dispatch function in a ref for navigationOptions to use
  React.useEffect(() => {
    if (props.navigation && dispatch) {
      props.navigation.setParams({dispatch});
    }
  }, [props.navigation, dispatch]);

  const [pendingCheckIns, setPendingCheckIns] = useState(0);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState('');

  const hasMomentsToSync = momentsToSync?.length > 0;

  // Debug function to sync all offline data
  const handleSyncAll = async () => {
    if (isSyncing) return;

    setIsSyncing(true);
    setSyncStatus('Syncing data...');

    try {
      await dispatch(syncMoments());

      setSyncStatus('Sync completed successfully');

      // Update pending check-ins count
      const count = await getPendingCheckInsCount();
      setPendingCheckIns(count);
    } catch (error) {
      console.error('Error during sync:', error);
      setSyncStatus(`Sync error: ${error.message}`);
    } finally {
      setIsSyncing(false);

      // Clear status after 3 seconds
      setTimeout(() => {
        setSyncStatus('');
      }, 3000);
    }
  };

  // Effects
  useEffect(() => {
    const fetchPendingCheckIns = async () => {
      const count = await getPendingCheckInsCount();
      setPendingCheckIns(count);
    };

    const checkIns = fetchPendingCheckIns();
  }, []);

  // Create handler functions using the helper functions
  const handleNameToFace = useCallback(() => {
    onNameToFace({navigation, dispatch, currentOrg, currentPerson});
  }, [navigation, dispatch, currentOrg, currentPerson]);

  const handleCheckIn = useCallback(() => {
    onCheckIn({navigation, dispatch, currentOrg});
  }, [navigation, dispatch, currentOrg]);

  const handleCheckOut = useCallback(() => {
    onCheckOut({navigation, dispatch, currentOrg, cachedPeople});
  }, [navigation, dispatch, currentOrg, cachedPeople]);

  const handleSleepMoment = useCallback(() => {
    onSleepMoment({navigation, dispatch, currentOrg, currentPerson});
  }, [navigation, dispatch, currentOrg]);

  const handlePottyMoment = useCallback(() => {
    onPottyMoment({navigation, dispatch, currentOrg, currentPerson});
  }, [navigation, currentOrg]);

  const handleFoodMoment = useCallback(() => {
    onFoodMoment({navigation, dispatch, currentOrg, currentPerson});
  }, [navigation, currentOrg]);

  const handleCommentMoment = useCallback(() => {
    onCommentMoment({navigation, dispatch, currentOrg, currentPerson});
  }, [navigation, currentOrg]);

  const offlineMoments = [
    {name: 'Check In', onPress: handleCheckIn},
    {name: 'Name To Face', onPress: handleNameToFace},
    {name: 'Sleep Moments', onPress: handleSleepMoment},
    {name: 'Potty Moment', onPress: handlePottyMoment},
    {name: 'Food Moment', onPress: handleFoodMoment},
    {name: 'Comment Moment', onPress: handleCommentMoment},
    {name: 'Check Out', onPress: handleCheckOut}
  ];
  return (
    <View style={localStyles.buttonContainer}>
      {offlineMoments.map(moment => (
        <TouchableOpacity
          key={moment.name}
          onPress={moment.onPress}
          style={localStyles.button}>
          <Text style={{color: colors.white, fontWeight: 'bold', fontSize: 18}}>
            {moment.name}
          </Text>
        </TouchableOpacity>
      ))}
      {/*Debug Buttons*/}
      {/*<View style={{
        flex: 1,
        flexDirection: 'column',
        alignItems: 'center',
        marginHorizontal: 16,
        marginVertical: 8,
        width: '100%',
      }}>
        <TouchableOpacity
          onPress={() => {
            const collection = Meteor.collection('people');
            const children = collection
              ? collection
                .find({})
                .map(
                  child =>
                    `${child.firstName} ${child.lastName}, checkedIn: ${child.checkedIn}`,
                )
                .sort()
              : [];

            Alert.alert('All Cached Children:', children.join('\n'), null, 2);
          }}
          style={[
            localStyles.button,
            {backgroundColor: colors.purpleLight, marginBottom: 8},
          ]}>
          <Text style={{color: colors.white, fontWeight: 'bold', fontSize: 18}}>
            Log Cached Children [FOR TESTING]
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            const moments = momentsToSync;
            Alert.alert(`Moments to sync: ${moments.length}`);
          }}
          style={[
            localStyles.button,
            {backgroundColor: colors.purpleLight, marginBottom: 8},
          ]}>
          <Text style={{color: colors.white, fontWeight: 'bold', fontSize: 18}}>
            Log Cached Moments [FOR TESTING]
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={async () => {
            dispatch({type: OFFLINE_CLEAR_MOMENTS});
            try {
              await AsyncStorage.multiSet([
                ['offline_checkins', JSON.stringify([])],
                ['offline_checkouts', JSON.stringify([])],
                ['offline_moments', JSON.stringify([])],
              ]);
              console.log('Cleared stored moments from AsyncStorage');
            } catch (error) {
              console.error('Error clearing stored moments:', error);
            }
          }}
          style={[
            localStyles.button,
            {backgroundColor: colors.purpleLight, marginBottom: 8},
          ]}>
          <Text style={{color: colors.white, fontWeight: 'bold', fontSize: 18}}>
            Clear Cached Moments
          </Text>
        </TouchableOpacity>

        Sync button
        <TouchableOpacity
          onPress={handleSyncAll}
          disabled={isSyncing}
          style={[
            localStyles.button,
            {backgroundColor: colors.green, marginBottom: 8},
          ]}>
          <Text style={{color: colors.white, fontWeight: 'bold', fontSize: 18}}>
            {isSyncing ? 'Syncing...' : 'Sync All Offline Data'}
          </Text>
        </TouchableOpacity>
      </View> */}
      </View>
  );
};

// Navigation options for the screen
Offline.navigationOptions = ({navigation, route}) => {
  const dispatch = useDispatch();

  return {
    title: 'Offline Mode',
    headerRight: () => (
      <View style={{flexDirection: 'row'}}>
        <TouchableWithoutFeedback
          onPress={() => {
            Alert.alert(
              'Go Online',
              'Would you like to sync your offline moments before going online?',
              [
                {text: 'Cancel', style: 'cancel'},
                {
                  text: 'Sync and Go Online',
                  onPress: () => {
                    dispatch(syncMoments());

                    // Then go online after a short delay to allow sync to start
                    setTimeout(() => {
                      dispatch(toggleOnlineMode());
                    }, 500);
                  },
                },
              ],
              {cancelable: false},
            );
          }}>
          <Nucleo name="icon-log-in" size={24} color={colors.primaryA} />
        </TouchableWithoutFeedback>
      </View>
    ),
  };
};

export default Offline;
