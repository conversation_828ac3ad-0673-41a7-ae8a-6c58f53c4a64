import {Alert} from 'react-native';
import {toggleOfflineMode, toggleOnlineMode} from '../../actions/userData';
import {getCachedPeople, updateCachedChildren} from './cacheHelpers';
import moment from 'moment-timezone';
import {
  nameToFace,
  saveMoment,
  addSleepMoments,
  updateOfflinePeople
} from "../../actions/offline";
import Meteor from 'react-native-meteor';

/**
 * Helper function to update person status in local Meteor collection for check-in
 * @param {Array} people - Array of people to check in
 * @param {Object} checkInData - Check-in data including timestamp and group info
 */
const updateLocalCheckInStatus = (people, checkInData) => {
  const timestamp = checkInData.timestamp || moment().valueOf();

  people.forEach(person => {
    const p = Meteor.collection('people').findOne(person._id);
    if (p) {
      p.checkedIn = true;
      p.checkInTime = timestamp;
      p.checkInGroupId = person.checkInGroupId || person.defaultGroupId;
      p.groupName = person.checkInGroupName || person.defaultGroupName;
      p.checkedInOutTime = timestamp;
      Meteor.collection('people').saveLocal(p);
      console.log(`Updated local Meteor collection for check-in: ${person._id}`);
    } else {
      console.warn(`Person not found in local Meteor collection: ${person._id}`);
      // If the person doesn't exist in the collection, insert them
      Meteor.collection('people').insert({
        ...person,
        checkedIn: true,
        checkInTime: timestamp,
        checkInGroupId: person.checkInGroupId || person.defaultGroupId,
        groupName: person.checkInGroupName || person.defaultGroupName,
        checkedInOutTime: timestamp,
        type: 'person'
      });
      console.log(`Inserted person into local Meteor collection: ${person._id}`);
    }
  });
};

/**
 * Helper function to update person status in local Meteor collection for check-out
 * @param {Object} person - Person to check out
 */
const updateLocalCheckOutStatus = (person) => {
  const timestamp = moment().valueOf();
  const p = Meteor.collection('people').findOne(person._id || person.tagId);
  if (p) {
    p.checkedIn = false;
    p.checkInGroupId = null;
    p.groupName = null;
    p.checkedInOutTime = timestamp;
    Meteor.collection('people').saveLocal(p);
    console.log(`Updated local Meteor collection for check-out: ${person._id}`);
  } else {
    console.warn(`Person not found in local Meteor collection for check-out: ${person._id}`);
  }
};

export const accountLongPress = props => {
  const {offlineMode, userPerson} = props;
  const userType = userPerson?.type;

  // Check if user is staff or admin before allowing offline mode access
  if (!offlineMode && !['staff', 'admin'].includes(userType)) {
    Alert.alert(
      'Access Restricted',
      'Offline mode is only available to staff and admin users.',
      [{text: 'OK', style: 'default'}],
      {cancelable: false}
    );
    return;
  }

  Alert.alert(
    `${offlineMode ? 'Go Online' : 'Go Offline'}`,
    `${offlineMode ? 'Be sure to sync any pending moments!' : 'limited functionality is available in offline mode. Would you like to proceed?'}`,
    [
      {text: 'Cancel', style: 'cancel'},
      {
        text: 'OK',
        onPress: () => {
          props.dispatch(
            offlineMode ? toggleOnlineMode() : toggleOfflineMode(),
          );
        },
      },
    ],
    {cancelable: false},
  );
};

/**
 * Common navigation helper to navigate to the tagger component
 * @param {Object} params - Navigation parameters
 * @param {Object} params.navigation - Navigation object
 * @param {Function} params.onSave - Callback function when people are selected
 * @param {Array} params.people - List of people to display in the tagger
 * @param {Object} params.currentOrg - Current organization
 * @param {String} params.title - Title for the tagger screen
 * @param {Boolean} params.checkInNotRequired - Whether check-in is required for the moment
 */
export const navigateToTagger = ({
  navigation,
  onSave,
  people,
  currentOrg,
  title = 'Tag People',
  checkInNotRequired = false,
}) => {
  navigation.navigate('offlineTagger', {
    onSave,
    taggedPeople: [],
    peopleOnly: true,
    currentOrg,
    people,
    title,
    checkInNotRequired,
  });
};

/**
 * Common navigation helper to navigate to the moment entry component
 * @param {Object} params - Navigation parameters
 * @param {Object} params.navigation - Navigation object
 * @param {Object} params.currentOrg - Current organization
 * @param {String} params.momentType - Type of moment
 * @param {Array} params.taggedPeople - List of tagged people
 * @param {String} params.taggedPeopleLabel - Label for the tagged people
 * @param {Object} params.currentPerson - Information about the person
 * @param {Function} params.dispatch - Dispatch function
 */
const navigateToMomentEntry = ({navigation, currentOrg, momentType, taggedPeople, taggedPeopleLabel, currentPerson, dispatch}) => {
  navigation.navigate('offlineMomentEntry', {
    taggedPeople,
    taggedPeopleLabel,
    userPerson: currentPerson,
    newMomentType: momentType.toLowerCase(),
    hidePeopleChooser: true,
    hideAddMediaButton: true,
    onSave: (momentData) => {
      dispatch(saveMoment(momentType, {
        momentType,
        taggedPeople,
        currentOrg,
        personId: currentPerson._id,
        date: momentData.date,
        time: momentData.time,
        momentFieldValues: momentData.momentFieldValues,
        createdAt: momentData.createdAt || new Date().valueOf(),
        attachedMedia: momentData.attachedMedia || {},
      }));
      navigation.navigate('offlineHome');
    }
  });
};

/**
 * Handler for Check In moment
 */
export const onCheckIn = ({navigation, dispatch, currentOrg}) => {
  // Get only checked-out children
  let checkedOutChildren = getCachedPeople(true)
  if (checkedOutChildren.length === 0) {
    Alert.alert(
      'No Children Available',
      'All children are already checked in.',
    );
    return;
  }

  // Navigate to the children tagger screen with check-in mode
  navigation.navigate('childrenTagger', {
    title: 'Select Children to Check In',
    type: 'checkIn', // Not strictly necessary as this is the default mode
    taggedPeople: [], // Initially no children are selected
    availablePeople: checkedOutChildren,
    currentOrg,
    onSave: checkinData => {
      console.log('Saving offline check-in');
      if (!checkinData.selectedChildren?.length) {
        navigation.navigate('offlineHome');
        return;
      }
      const timestamp = checkinData.timestamp;
      const updates = checkinData.selectedChildren.map(child => ({
        ...child,
        checkedIn: true,
        checkInTime: timestamp,
        checkInGroupId: checkinData.groupId || child.defaultGroupId || null,
        checkInGroupName:
          checkinData.groupName || child.defaultGroupName || null,
      }));

      // Update local Meteor collection with check-in status
      updateLocalCheckInStatus(updates, checkinData);

      // Create individual check-in moments for each child
      updates.forEach(child => {
        dispatch(saveMoment('checkIn', {
          personId: child._id,
          currentOrg,
          date: new Date(),
          time: new Date(),
          createdAt: timestamp || new Date().valueOf(),
          momentType: 'checkIn',
          offlineType: 'checkIn',
          groupId: child.checkInGroupId,
          groupName: child.checkInGroupName,
          prettyTime: moment(timestamp).format("h:mm a"),
          comments: checkinData.comment || '', // Server expects 'comments' not 'comment'
          momentFieldValues: {
            comment: checkinData.comment,
          },
        }));
      });

      // Update the Redux state with the fresh list of people
      dispatch(updateOfflinePeople());

      console.log('Check-in moments saved for syncing', updates.length, 'children');
      console.log(`Updated cached data for ${updates.length} people locally`);
      navigation.navigate('offlineHome');
    },
  });
};

/**
 * Handler for Check Out moment
 */
export const onCheckOut = ({navigation, dispatch, currentOrg, cachedPeople}) => {
  // Get only checked-in children
  const checkedInPeople = getCachedPeople()
    .filter(p => p.checkedIn)

  if (checkedInPeople.length === 0) {
    Alert.alert(
      'No People Available',
      'There are no checked-in people to check out.',
    );
    return;
  }

  // Navigate to the children tagger screen with checkout mode
  navigation.navigate('childrenTagger', {
    title: 'Select People to Check Out',
    type: 'checkOut',
    taggedPeople: [],
    currentOrg,
    people: checkedInPeople,
    onSave: checkoutData => {
      console.log('Saving checkout data:', checkoutData);

      // Get the person object from the ID
      const personId = checkoutData.personId;
      const person = cachedPeople.find(p => p._id === personId);

      if (!person) {
        console.error('Person not found in cached people:', personId);
        return;
      }

      // Update local Meteor collection with check-out status
      updateLocalCheckOutStatus(person);

      // Save the checkout moment for syncing (this handles all the data we need)
      dispatch(saveMoment('checkOut', {
        ...checkoutData,
        currentOrg,
        personId: person._id,
        date: checkoutData.date || new Date(),
        time: checkoutData.time || new Date(),
        createdAt: checkoutData.createdAt || new Date().valueOf(),
      }));

      // Update the Redux state with the fresh list of people
      dispatch(updateOfflinePeople());

      console.log(`Updated cached data for person ${person._id} locally`);

      navigation.navigate('offlineHome');
    },
  });
};

/**
 * Handler for Name to Face moment
 */
export const onNameToFace = ({navigation, dispatch, currentOrg, currentPerson}) => {
  const cachedPeople = getCachedPeople()

  // Make sure the current person has a group assigned
  if (!currentPerson.checkInGroupId) {
    Alert.alert(
      "Group Required",
      "You must be checked into a group to perform a Name to Face check."
    );
    return;
  }

  const childrenInCurrentGroup = cachedPeople.filter(p => p.checkInGroupId === currentPerson.checkInGroupId);

  // Check if there are any children in the current group
  if (childrenInCurrentGroup.length === 0) {
    Alert.alert(
      "No Children Found",
      "There are no children checked into your current group."
    );
    return;
  }

  const saveNameToFace = (taggedPeople) => {
    if (taggedPeople?.length === 0) {
      Alert.alert("No Children Selected", "Please select at least one child for Name to Face.");
      return;
    }

    // Log the format of taggedPeople for debugging
    console.log('Tagged people format in saveNameToFace:', JSON.stringify(taggedPeople, null, 2));

    // The taggedPeople from the tagger component already has the format we need
    // with tagId and tagLabel properties
    const childrenNames = taggedPeople
      .map(child => child.tagLabel)
      .join(', ');

    // Pass the taggedPeople to nameToFace action
    // The action will handle the conversion internally using our helper function
    dispatch(nameToFace(taggedPeople));

    Alert.alert(
      "Name to Face Recorded",
      `Name to Face for ${taggedPeople.length} children recorded:\n\n${childrenNames}\n\nThis will be synced when you go online.`
    );

    navigation.navigate('offlineHome')
  };

  navigateToTagger({
    navigation,
    onSave: saveNameToFace,
    people: childrenInCurrentGroup,
    currentOrg,
    title: 'Select Children for Name to Face',
  });
};

/**
 * Handler for Sleep moment
 */
export const onSleepMoment = ({navigation, dispatch, currentOrg, currentPerson}) => {
  const people = getCachedPeople();

  navigation.navigate('offlineSleepMoments', {
    navigation,
    people,
    currentOrg,
    title: 'Select Children for Sleep Moment',
    onAddSleepMoment: taggedPeople => {
      navigateToTagger({
        navigation,
        people,
        currentOrg,
        title: 'Tag Children for Sleep Moment',
        onSave: (taggedPeople) => {
          dispatch(addSleepMoments(taggedPeople));
          navigation.navigate('offlineHome');
        },
      });
    }
  });
};

/**
 * Handler for Potty moment
 */
export const onPottyMoment = ({navigation, dispatch, currentOrg, currentPerson}) => {
  const people = getCachedPeople();

  navigateToTagger({
    navigation,
    people,
    currentOrg,
    title: 'Select Children for Potty Moment',
    onSave: (taggedPeople) => {
      if (taggedPeople?.length === 0) return;
      navigateToMomentEntry({
        navigation,
        currentOrg,
        taggedPeople,
        currentPerson,
        dispatch,
        momentType: 'potty',
        taggedPeopleLabel: taggedPeople.map(tag => tag.tagLabel).join(', '),
      })
    },
  });
};

/**
 * Handler for Food moment
 */
export const onFoodMoment = ({navigation, currentOrg, currentPerson, dispatch}) => {
  const people = getCachedPeople();

  navigateToTagger({
    navigation,
    people,
    currentOrg,
    onSave: (taggedPeople) => {
      navigateToMomentEntry({
        navigation,
        currentOrg,
        taggedPeople,
        dispatch,
        currentPerson,
        momentType: 'food',
        taggedPeopleLabel: taggedPeople.map(tag => tag.tagLabel).join(', '),
      })
    },
    title: 'Select Children for Food Moment',
    checkInNotRequired: true,
  });
};

/**
 * Handler for Comment moment
 */
export const onCommentMoment = ({navigation, currentOrg, currentPerson, dispatch}) => {
  const people = getCachedPeople();

  navigateToTagger({
    navigation,
    people,
    currentOrg,
    onSave: (taggedPeople) => {
      navigateToMomentEntry({
        navigation,
        currentOrg,
        taggedPeople,
        dispatch,
        currentPerson,
        momentType: 'comment',
        taggedPeopleLabel: taggedPeople.map(tag => tag.tagLabel).join(', '),
      })
    },
    title: 'Select Children for Comment',
    checkInNotRequired: true,
  });
};

/**
 * Creates a Name to Face moment object from tagged people data
 *
 * @param {Array} taggedPeople - Array of tagged people, can be in various formats:
 *   - Objects with tagId/tagLabel: [{tagId: "123", tagLabel: "Name"}]
 *   - Full person objects: [{_id: "123", firstName: "John", lastName: "Doe"}]
 *   - Array of person IDs: ["123", "456"]
 * @param {Object} personInfo - Current person information
 * @param {Object} orgInfo - Current organization information
 * @param {Array} cachedPeople - Cached people data (used when taggedPeople is an array of IDs)
 * @returns {Object} Structured moment object with all required fields
 */
export const createNameToFaceMoment = (taggedPeople, personInfo, orgInfo, cachedPeople = []) => {
  const now = new moment();

  // Create a description of the confirmed people
  let momentDescription = "";

  // Extract IDs from taggedPeople if it contains objects
  let personIds = [];
  if (taggedPeople && taggedPeople.length > 0) {
    console.log('Processing taggedPeople in createNameToFaceMoment:', JSON.stringify(taggedPeople, null, 2));

    if (typeof taggedPeople[0] === 'object') {
      if (taggedPeople[0].tagId) {
        // Format from the tagger component: [{tagId: "123", tagLabel: "Name"}]
        personIds = taggedPeople.map(p => p.tagId);

        // Create description directly from the tagLabel
        taggedPeople.forEach(person => {
          momentDescription += `Confirmed ${person.tagLabel}\n`;
        });

        console.log('Extracted tagIds from taggedPeople:', personIds);
      } else if (taggedPeople[0]._id) {
        // Format with full person objects: [{_id: "123", firstName: "John", ...}]
        personIds = taggedPeople.map(p => p._id);

        // Create description from the person objects
        taggedPeople.forEach(person => {
          momentDescription += `Confirmed ${person.firstName} ${person.lastName}\n`;
        });

        console.log('Extracted _ids from taggedPeople:', personIds);
      }
    } else {
      // taggedPeople is already an array of IDs
      personIds = taggedPeople;
      console.log('taggedPeople is already an array of IDs:', personIds);

      // Get the names of the tagged people for the description
      if (cachedPeople && cachedPeople.length > 0) {
        const taggedPeopleDetails = cachedPeople.filter(person =>
          personIds.includes(person._id)
        );

        taggedPeopleDetails.forEach(person => {
          momentDescription += `Confirmed ${person.firstName} ${person.lastName}\n`;
        });
      }
    }
  }

  // Store the offline data needed for syncing
  const offlineData = {
    selectedPeople: personIds,
    completedById: personInfo?._id,
    filterGroupId: personInfo?.checkInGroupId || personInfo?.defaultGroupId
  };

  // Create the moment object
  const newMoment = {
    offlineType: 'nameToFace',
    momentType: 'nameToFace',
    momentTypePretty: 'Name To Face',
    taggedPeople,
    createdAt: now.valueOf(),
    sortStamp: now.valueOf(),
    date: now.format("MM/DD/YYYY"),
    time: now.format("h:mm a"),
    prettyTime: now.format("h:mm a"),
    currentPerson: personInfo,
    currentOrg: orgInfo,
    nameToFaceGroupId: personInfo?.checkInGroupId || personInfo?.defaultGroupId,
    nameToFaceClassroomGroupId: personInfo?.checkInGroupId || personInfo?.defaultGroupId,
    owner: personInfo?._id,
    comment: `${taggedPeople.length}/${taggedPeople.length} confirmed.\n`,
    nameToFaceDescription: momentDescription,
    offlineData
  };

  return newMoment;
};
