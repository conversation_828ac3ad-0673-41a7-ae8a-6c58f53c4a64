import React, { useState } from 'react';
import { StyleSheet, Alert, Platform, View } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Container, Textarea, <PERSON><PERSON>, Header, Content, Body, Title, Text, Left, Right, Item, Input, Label, Picker, <PERSON><PERSON>, Spinner, StyleProvider } from 'native-base';
import DateTimePicker from 'react-native-modal-datetime-picker';
import _ from 'underscore';
import moment from 'moment';
import SharedStyles from '../../../shared/SharedStyles';
import getTheme from '../../../../native-base-theme/components';
import commonColor from '../../../../native-base-theme/variables/commonColor';
import colors from '../../../config/colors.json';
import { connect } from 'react-redux';
import { getCachedPeople, updateCachedChildren } from '../cacheHelpers';

/**
 * Initial state for the check-in form
 */
const initialState = {
  showTimePicker: false,
  time: new Date(),
  selectedGroup: "",
  comment: "",
  isSaving: false
};

/**
 * Creates check-in data object for storage
 * @param {Object} params - Parameters for check-in data
 * @returns {Object} Check-in data object
 */
const createCheckInData = ({ personId, groupId, groupName, time, comment, timestamp }) => ({
  personId,
  groupId,
  groupName,
  prettyTime: moment(time).format("h:mm a"),
  comment,
  timestamp,
  isOffline: true
});

/**
 * Creates updated person object for cache
 * @param {Object} params - Parameters for person update
 * @returns {Object} Updated person object
 */
const createUpdatedPerson = ({ person, groupId, groupName, timestamp }) => ({
  _id: person._id,
  firstName: person.firstName,
  lastName: person.lastName,
  name: `${person.firstName} ${person.lastName}`,
  type: 'person',
  checkedIn: true,
  checkInGroupId: groupId,
  checkInGroupName: groupName,
  lastCheckIn: timestamp,
  checkInTime: timestamp,
  defaultGroupId: person.defaultGroupId,
  defaultGroupName: person.defaultGroupName
});

/**
 * Saves check-in data to offline storage
 * @param {Object} checkInData - Check-in data to save
 */
const saveCheckInToStorage = async (checkInData) => {
  console.log("=== Saving check-in to storage ===");
  console.log("Check-in data to save:", checkInData);

  try {
    const offlineCheckInsJson = await AsyncStorage.getItem('offline_check_ins') || '[]';
    console.log("Current storage data:", offlineCheckInsJson);

    const offlineCheckIns = JSON.parse(offlineCheckInsJson);
    console.log("Parsed current check-ins:", offlineCheckIns);

    offlineCheckIns.push(checkInData);
    console.log("Updated check-ins array:", offlineCheckIns);

    await AsyncStorage.setItem('offline_check_ins', JSON.stringify(offlineCheckIns));
    console.log("Successfully saved to storage");
  } catch (error) {
    console.error("Error saving check-in to storage:", error);
    throw error;
  }
};

/**
 * Verifies that the check-in was successful
 * @param {string} personId - ID of the person to verify
 * @returns {boolean} Whether verification passed
 */
const verifyCheckIn = (personId) => {
  const currentCache = getCachedPeople();
  const updatedChild = currentCache.find(c => c._id === personId);
  console.log('Check-in verification:', {
    found: !!updatedChild,
    checkedIn: updatedChild?.checkedIn
  });
  return updatedChild?.checkedIn === true;
};

const OfflineCheckInModal = ({ route, navigation, groups }) => {
  // Now we expect an array of people instead of a single person
  const people = route?.params?.people || [];
  const [state, setState] = useState({
    ...initialState,
    // Use the default group of the first person if available
    selectedGroup: people[0]?.defaultGroupId || ""
  });

  const handleGroupChange = (value) => {
    setState(prev => ({
      ...prev,
      selectedGroup: value,
      isSaving: false
    }));
  };

  const formatPeopleNames = (peopleArray) => {
    return peopleArray.map(p => `${p.firstName} ${p.lastName}`).join(', ');
  };

  const handleSave = async () => {
    try {
      setState(prev => ({ ...prev, isSaving: true }));

      const groupId = state.selectedGroup;
      const group = groupId && _.find(groups, (g) => g._id === groupId);
      const timestamp = new Date().getTime();
      const groupName = group?.name || '';

      // Process each person
      for (const person of people) {
        // Create and save check-in data for each person
        const checkInData = createCheckInData({
          personId: person._id,
          groupId,
          groupName,
          time: state.time,
          comment: state.comment,
          timestamp
        });
        await saveCheckInToStorage(checkInData);

        // Update each person's data in cache
        const updatedPerson = createUpdatedPerson({
          person,
          groupId,
          groupName,
          timestamp
        });
        await updateCachedChildren([updatedPerson]);

        // Verify the update for each person
        const isVerified = verifyCheckIn(person._id);
        if (!isVerified) {
          console.warn(`Check-in verification failed for ${person.firstName}, retrying update...`);
          await updateCachedChildren([updatedPerson]);
        }
      }

      // Handle navigation and callbacks
      if (route?.params?.rerender) {
        await new Promise(resolve => setTimeout(resolve, 100));
        route.params.rerender();
      }

      if (route?.params?.navigateToHome) {
        navigation.navigate("offlineHome");
      } else {
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error during check-in:', error);
      Alert.alert('Error', 'Failed to save check-in data offline');
    } finally {
      setState(prev => ({ ...prev, isSaving: false }));
    }
  };

  return (
    <StyleProvider style={getTheme(commonColor)}>
      <Container>
        <Header style={{backgroundColor: colors.white}}>
          <Left style={{flex:1}}>
            <Button transparent onPress={() => navigation.goBack()}>
              {Platform.OS === 'ios' ? (
                <Text style={SharedStyles.headerTitleStyle}>Cancel</Text>
              ) : (
                <Icon name='arrow-back-circle' style={{color: colors.primaryA}}></Icon>
              )}
            </Button>
          </Left>
          <Body style={{flex:1,justifyContent:'center',alignItems:'center'}}>
            <Title style={SharedStyles.headerTitleStyle}>Check In</Title>
          </Body>
          <Right style={{flex:1}}>
            {state.isSaving ? (
              <Spinner color={colors.primaryA}/>
            ) : (
              <Button transparent onPress={handleSave}>
                <Text style={SharedStyles.headerTitleStyle}>Save</Text>
              </Button>
            )}
          </Right>
        </Header>
        <Content>
          <Item stackedLabel disabled style={{minHeight: 60}}>
            <Label>Person{people.length > 1 ? 's' : ''}</Label>
            <Input
              disabled
              multiline
              style={{
                flexWrap: 'wrap',
                paddingTop: 8,
                paddingBottom: 8,
                minHeight: 40
              }}
            >{formatPeopleNames(people)}</Input>
          </Item>
          <Item stackedLabel style={{flexDirection: 'column', alignItems: 'flex-start'}}>
            <Label>Time</Label>
            <Button transparent onPress={() => setState(prev => ({ ...prev, showTimePicker: !prev.showTimePicker }))}>
              <Text style={{paddingLeft:5}}>{moment(state.time).format("h:mm a")}</Text>
            </Button>
            <DateTimePicker
              isVisible={state.showTimePicker}
              onConfirm={(datetime) => setState(prev => ({ ...prev, time: datetime, showTimePicker: false }))}
              onCancel={() => setState(prev => ({ ...prev, showTimePicker: false }))}
              mode="time"
            />
          </Item>
          <Item stackedLabel disabled style={{flexDirection: 'column', alignItems: 'flex-start'}}>
            <Label>Group</Label>
            <Picker
              note
              style={{marginLeft:0, paddingLeft:0, width:"100%"}}
              mode="dropdown"
              placeholder="Choose group"
              placeholderStyle={{ color: colors.primaryA }}
              textStyle={{ color: colors.primaryA }}
              placeholderIconColor={colors.blue}
              selectedValue={state.selectedGroup}
              onValueChange={handleGroupChange}
              headerBackButtonTextStyle={{ color: colors.primaryA }}
            >
              <Picker.Item label="None" value="" style={{color: colors.black,backgroundColor: colors.white}}/>
              {groups?.map((g) =>
                <Picker.Item key={g._id} label={g.name} value={g._id} style={{color: colors.black,backgroundColor: colors.white}}/>
              )}
            </Picker>
          </Item>
          <Item stackedLabel>
            <Label>Comment</Label>
            <Textarea
              onChangeText={(comment) => setState(prev => ({ ...prev, comment }))}
              value={state.comment}
              rowSpan={5}
              style={{width:"100%"}}
              placeholder="Enter any special notes for the day..."
            />
          </Item>
        </Content>
      </Container>
    </StyleProvider>
  );
};

const mapStateToProps = (state) => ({
  groups: state.auth.offlineGroups,
});

export default connect(mapStateToProps)(OfflineCheckInModal);
