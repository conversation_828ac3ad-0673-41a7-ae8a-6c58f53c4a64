import React from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import Nucleo from '../../../components/icons/Nucleo';
import ModalWebView from "../../../components/ModalWebView";
import colors from '../../../config/colors.json';
import styles from './styles';

const resources = [
	{ title: "Tortal", description: "Lightbridge Training", url: "https://lightbridge.tortal.net/"},
	{ title: "Intranet", description: "Lightbridge News and Documentation", url: "https://www83lightbridgeacademy.franconnect.net/fc/"}
];

class LightBridgeTortal extends React.PureComponent {
	constructor(props) {
		super(props);

		this.state = {
			modalVisible: props.visible,
			modalWebViewVisible: false
		}
	}

	openResource(item) {
		this.setState({
			modalWebViewVisible: true,
			modalWebViewTitle: item.title,
			modalWebViewUrl: item.url
		});
	}

	closeModalWebView() {
		this.setState({modalWebViewVisible:false});
	}

	render() {

		return (
			<View style={styles.card}>
				<View style={styles.headerLabelView}>
					<Nucleo name="icon-c-info" size={16} color={colors.primaryA} style={{marginRight:8}}/>
					<Text style={{fontWeight: 'bold', color: colors.primaryA}}>Important Resources</Text>
				</View>
				<Text style={{color: colors.black, fontSize: 16, marginBottom: 24}}>Here are links to important resources:</Text>
				{
					resources.map((resource) => 
						<TouchableOpacity 
							onPress={() => this.openResource(resource)} 
							style={{flex: 1, marginBottom: 16, padding: 16, borderColor: colors.primaryA, borderWidth: 1, borderRadius: 16}}
						>
							<View style={{flexDirection: 'row', alignItems: 'center'}}>
								<View style={{flexDirection: 'column'}}>
									<Text style={{fontWeight: 'bold', color: colors.primaryA, fontSize: 20, marginBottom: 4}}>{resource.title}</Text>
									<Text style={{color: `${colors.black}80`, fontSize: 14}}>{resource.description}</Text>
								</View>
								<View style={{flex: 1}}/>
								<Nucleo name="icon-launch-app" size={16} color={colors.primaryA}/>
							</View>
						</TouchableOpacity>
					)
				}
				<ModalWebView 
					onDismiss={() => this.closeModalWebView()}
					visible={this.state.modalWebViewVisible}
					title={this.state.modalWebViewTitle}
					url={this.state.modalWebViewUrl}
				/>
			</View>
		)
	}
}

export default LightBridgeTortal;
