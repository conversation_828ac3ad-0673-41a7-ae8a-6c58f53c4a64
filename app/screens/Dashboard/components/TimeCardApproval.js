import React from 'react'
import { View, Text, TouchableOpacity, Modal, Appearance, FlatList, Alert} from 'react-native';
import {Container, Header, Left, Body, Right, Title, Icon, Button} from 'native-base';
import DateTimePicker from 'react-native-modal-datetime-picker';
import Meteor from 'react-native-meteor';
import colors from '../../../config/colors.json';
import {scale, verticalScale, moderateScale} from '../../../shared/Scaling';
import Nucleo from '../../../components/icons/Nucleo';
import moment from 'moment';

import styles from './styles';
import _ from 'lodash';

const localStyle = {
  buttonView: {
    marginVertical: moderateScale(10), 
    flexDirection: 'row', 
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: moderateScale(14),
    width:moderateScale(28),
    height:moderateScale(28),
    backgroundColor: colors.gray
  },
  container: { 
    flexDirection: 'column', 
    flex: 1, 
    marginTop: 12, 
    borderColor: colors.white, 
  },
  dateLabelText: {
    color: colors.black,
    fontWeight: "bold",
    fontSize: 22,
    padding: 6
  },
  summaryLabelText: {
    color: colors.black,
    fontWeight: "bold",
    fontSize: 16,
    padding: 6
  },
  list: {
    flex: 1,
    backgroundColor: `${colors.primaryA}1A`,
  },
}

const initialState = {
  hasTimeToApprove: false,
  modalVisible: false,
  periodLabel: null,
  timecards: null,
  selectedItems: []
};

class TimeCardApproval extends React.Component {

  constructor(props) {
    super(props);
    this.state = { ...initialState };
  };

  _getTimeCardData = (datetime) => {
	this._callGetTimeCardData(datetime);
  }

  _callGetTimeCardData(datetime) {
	Meteor.call("getTimecardsForReview", {datetimeInPeriod: datetime ? new moment(datetime).format("YYYY-MM-DD") : null}, (error, res) => {
		if (error) {
		  console.log(error);
		}
		
		if (res) {
		  // console.log("got tcs", res)
		  let timecards = res?.timecards?.filter(data => !data?.void);
		  this.setState({
			  hasTimeToApprove: timecards?.filter(tc => !tc.approvedAt)?.length > 0,
			  periodLabel: res.label,
			  timecards: timecards,
			  totalMinutesWorked: res.totalMinutesWorked,
			  totalMinutesApproved: res.totalMinutesApproved,
			  selectedItems: [],
			  currentDateTime: datetime
		  });
		}
	  });
  }

  _approveTimecards = () => {
	Meteor.call("approveTimecards", {timecardIds: this.state.selectedItems}, (error, res) => {
		if (error) {
			console.log(error);
		} else {
			Alert.alert("Timecard(s) approved successfully.");
			this._callGetTimeCardData(this.state.currentDateTime);
		}
	});
  }
  
  componentDidMount() {
    this._getTimeCardData();
  }

  onSelectPeriod() {

  }

  _convertToDate(fieldValue) {
		
	if (typeof fieldValue == "string" || fieldValue instanceof String) {
		return new moment(fieldValue, "h:mm a").toDate();
	} else
		return new moment(fieldValue).toDate();
  }

  selectItem = (item) => {
	console.log("item", item, this.state.selectedItems);
	const itemIndex = this.state.selectedItems.indexOf(item._id);
	if (itemIndex < 0) {
		this.setState({selectedItems: [...this.state.selectedItems, item._id]});
	} else {
		const newArray = this.state.selectedItems.splice(itemIndex);
		this.setState({selectedItems: [...this.state.selectedItems]});
	}
  }

  renderItem = ({ item, index }) => {
	return (
	<TouchableOpacity style={{flex:1, flexDirection: 'row', marginVertical: 16, alignItems: 'center', padding:3}} onPress={() => this.selectItem(item)}>
	
		<Text style={{flex:1, color: colors.black, fontSize: 18}}>{item.checkInDate}: {item.checkInTime} - {item.checkOutTime}</Text>
		<View>
			{item.approvedAt ?
				<Icon active name="check" type="FontAwesome5" style={{fontSize: 18, color: colors.grayDark, marginRight:8}}></Icon>
				:
				this.state.selectedItems.includes(item._id) ? 
					<Icon active name="check-circle" type="FontAwesome5" style={{fontSize: 18, color: colors.limeGreen, marginRight:8}}></Icon>
					:
					<Icon active name="circle" type="FontAwesome5" style={{fontSize: 18, color: colors.grayDark, marginRight:8}}></Icon>
			}
		</View>
	</TouchableOpacity>  
	)
  }

  renderSeparator = () => (
	<View
		style={{
			flex: 1,
			backgroundColor: colors.grayDark,
			height: 0.5,
			marginLeft: 0,
		}}
	/>
)

  render () {
    const { hasTimeToApprove, periodLabel, timecards, totalMinutesWorked, totalMinutesApproved } = this.state;
    if (!hasTimeToApprove) return null;

	const timeReportedLabel = !isNaN(totalMinutesWorked) ? `${Math.floor(totalMinutesWorked / 60)} h ${totalMinutesWorked % 60} m` : "";
	
	return (
		<View style={styles.card}>
			<Modal
			animationType="slide"
			transparent={false}
			visible={this.state.modalVisible}
			onRequestClose={() => {
			}}>
				<Container>
					<Header style={{ backgroundColor: colors.white}}>
						<Left></Left>
						<Body style={{flex:3}}><Title style={{color: colors.primaryA}}>Review Time Cards</Title></Body>
						<Right><Button transparent onPress={()=> {this.setState({modalVisible:false});}}><Text style={{color: colors.primaryA}}>Dismiss</Text></Button></Right>
					</Header>
					<View style={{padding:0, flexDirection:"column", flex: 1}}>
						<Text style={localStyle.dateLabelText}>Period: {periodLabel}</Text>
						<Text style={localStyle.summaryLabelText}>Time Reported: {timeReportedLabel}</Text>
						<TouchableOpacity 
							onPress={() => this.setState({showDatePicker: true})} 
							style={[styles.button,{flex:0}]}
						>
							<Text style={styles.buttonText}>Change Period</Text>
						</TouchableOpacity>
						
						<DateTimePicker
							isVisible={this.state.showDatePicker}
							onConfirm={(datetime)=>{this._getTimeCardData(datetime); this.setState({showDatePicker: !this.state.showDatePicker})}}
							onCancel={()=> {this.setState({showDatePicker: !this.state.showDatePicker})}}
							mode="date"
							date={this._convertToDate(this.state.datePickerDate)}
							isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
						/>
						<FlatList 
							style={localStyle.list}
							data={timecards}
							renderItem={this.renderItem}
							ItemSeparatorComponent={this.renderSeparator}
							keyExtractor={(item) => item._id} 
						/>
						<TouchableOpacity 
							onPress={() => this._approveTimecards()} 
							style={[styles.button,{flex:0,paddingBottom:24}]}
						>
							<Text style={styles.buttonText}>Approve Selected</Text>
						</TouchableOpacity>
					</View>
				</Container>
			</Modal>
			<View>
				<View style={styles.headerLabelView}>
					<Nucleo name="icon-survey" size={16} color={colors.primaryA} style={{marginRight:8}}/>
					<Text style={{fontWeight: 'bold', color: colors.primaryA}}>Time Card Approval</Text>
				</View>

				<View style={{flex: 1, flexDirection: 'column'}}>
					<Text style={{fontWeight: 'bold', fontSize: 24, color: colors.black}}>You've got time to approve</Text>
					<View style={{flex: 1, flexDirection: 'row', justifyContent: 'space-between', marginTop: 8, paddingHorizontal:0}}>
					 	
						<TouchableOpacity 
							onPress={() => this.setState({modalVisible: true})} 
							style={styles.button}
						>
							<Text style={styles.buttonText}>Review</Text>
						</TouchableOpacity>
					
					</View>
				</View>
			</View>
		</View>
	)
  }
}

export default TimeCardApproval;