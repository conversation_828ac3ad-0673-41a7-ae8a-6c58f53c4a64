import React from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import AvatarImage from '../../../components/AvatarImage';
import Nucleo from '../../../components/icons/Nucleo';
import colors from '../../../config/colors.json';
import styles from './styles';

const localStyles = {
  container: { 
    flexDirection: 'row', 
    flex: 1,
    marginTop: 24,
    marginBottom: 8
  },
  textContainer: {
    flexDirection: 'column',
    flex: 1,
    marginLeft:16
  },
  button: { 
    borderRadius: 4, 
    backgroundColor: colors.primaryA, 
    flexDirection: 'row', 
    flex: 1, 
    paddingVertical: 10, 
    paddingHorizontal: 10, 
    alignItems: 'center', 
    justifyContent: 'center',
  },
  buttonText: {
    color: colors.white, 
    fontSize: 14, 
    fontWeight: 'bold',
  },
}

class WhoAmI extends React.Component {

  onCheckIn() {
    const { props,person } = this.props;
    if(person?.checkedIn) return
    props?.navigation?.navigate('CheckInModal', {person: person});
  }

	render() {
    const { person  } = this.props;
    if (!person) return null;
    const checkInText = person.checkedIn == true ? person.checkInGroupName ? `You are checked into ${person.checkInGroupName}` : `You are checked in`: `You are not checked in`;
		return (
			<View style={styles.transparentCard}>
        <TouchableOpacity onPress={this.onCheckIn.bind(this)}
        activeOpacity={1}>
        <View style={localStyles.container}>
          <AvatarImage 
							source={person.getAvatarUrl()} 
							initials={person.personInitials()} 
							style={styles.photo} 
							defaultPhotoContainerStyle={styles.defaultPhotoContainer} 
							defaultPhotoTextStyle={styles.defaultPhotoTextStyle} 
					/>
          <View style={localStyles.textContainer}>
            <Text style={{color: colors.black, fontSize: 24, marginBottom: 4, fontWeight: "bold"}}>Hey {person.firstName}!</Text>
            <Text style={{color: colors.black, fontSize: 16, marginBottom: 0}}>{checkInText}</Text>
          </View>
        </View>
        </TouchableOpacity>
				
			</View>
		)
	}
}

export default WhoAmI;
