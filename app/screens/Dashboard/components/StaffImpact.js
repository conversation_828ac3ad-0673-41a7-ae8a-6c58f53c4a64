import React from 'react';
import { Text, View, Image } from 'react-native';
import { ActionSheet, Icon, Card, CardItem, Button, Left, Body, Container, Segment, StyleProvider, Right} from 'native-base';
import Meteor, { withTracker } from 'react-native-meteor';
import Nucleo from '../../../components/icons/Nucleo';
import sharedStyles from '../../../shared/SharedStyles';
import colors from '../../../config/colors.json';

import styles from './styles';

const iconMap = {
	Reactions: require('../../../images/reactions/thumb-up-2.png'),
	Views: require('../../../images/reactions/eye-recognition.png'),
	Moments: require('../../../images/reactions/comment-add.png')	
};

import _ from 'underscore';

class StaffImpact extends React.PureComponent {
	constructor(props) {
		super(props);
		this.state = {
			staffImpactData: { 
				details: [],
				message: "Be great today! Check here for an update on your progress.",
			},
			retrievedOnce: false,
		}
	}

	_fetchData() {
		Meteor.call("staffImpactSummary", (error, result) => {	
			this.setState({staffImpactData:result, retrievedOnce: true});
		});
	}

	UNSAFE_componentWillMount() {
		this._fetchData();
	}

	UNSAFE_componentWillReceiveProps(props) {
		const { refreshRequestAt } = this.props;
		if (props.refreshRequestAt !== refreshRequestAt) {
		  this._fetchData();
		}
	}

	render() {
		if (!this.state.retrievedOnce) return null;
		
		let details = this.state?.staffImpactData?.details ?? [];
		const hasMessage = (this.state?.staffImpactData?.message) ? true : false;

		_.each(details, (dataItem) => {
			dataItem.icon = iconMap[dataItem.label] || iconMap["Reactions"];
		});
		
		return (
			<View style={styles.card}>
				<View style={styles.headerLabelView}>
					<Nucleo name="icon-archery-target" size={16} color={colors.primaryA} style={{marginRight:8}}/>
					<Text style={{fontWeight: 'bold', color: colors.primaryA}}>Your Impact Today</Text>
				</View>
				{details.map((dataItem, index) => 
					<View 
						key={`${dataItem.label}-${index}`}
						style={{flexDirection:"row", marginBottom: 24, marginHorizontal: 32, alignItems: 'center'}}>
						<Image source={dataItem.icon} style={{height: 36, width: 36, marginRight:16}}/>
						<Text style={{fontSize:18, color: colors.black}}>{dataItem.label}</Text>
						<View style={{flex: 1}} />
						<View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center', backgroundColor: colors.white, borderColor: `${colors.primaryA}80`, borderWidth: 1, borderRadius: 20, width: 40, height: 40}}>
							<Text style={{fontSize: 20, fontWeight: 'bold', color: colors.primaryA}}>{dataItem.count}</Text>
						</View>
					</View>
				)}
				{hasMessage &&
					<Text style={{fontSize: 20, color: colors.black, marginHorizontal: 32, textAlign: 'center'}}>{this.state.staffImpactData.message}</Text>
				}
			</View>
		)
	}
}

export default StaffImpact;
