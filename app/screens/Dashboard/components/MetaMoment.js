import React from 'react'
import { View, Text } from 'react-native';
import { ActionSheet } from 'native-base';
import Nucleo from '../../../components/icons/Nucleo';
import MetaMoments from '../../../api/MetaMoments';
import colors from '../../../config/colors.json';
import styles from './styles';
import _ from 'lodash';
import Meteor, { withTracker } from 'react-native-meteor';

import MetaSleepMomentDetails from './MetaSleepMomentDetails';

class MetaMoment extends React.Component {

  render () {
    const { metaMoments, subsLoading } = this.props;
    if (subsLoading) return null;
    if (metaMoments.length > 0) {
      return (
        <View style={styles.card} key={`metamoment-combined-sleep`}>
          <View style={[styles.headerLabelView, {marginBottom: 0}]}>
            <Nucleo name="icon-bedroom" size={16} color={colors.primaryA} style={{marginRight:8}}/>
            <Text style={{fontWeight: 'bold', color: colors.primaryA}}>Who's Asleep</Text>
          </View>
          { 
            metaMoments.map((m) => (
              <MetaSleepMomentDetails momentData={m} />
            ))
          }
        </View>
      );
    }
    return null;
  }
}

export default withTracker(params => {
  let groupId = (params.person) ? (params.person.checkInGroupId || params.person.defaultGroupId) : null;
  const metaMomentsHandle = Meteor.subscribe("metaMoments", { groupId });
  return {
    subsLoading: !metaMomentsHandle.ready(),
    metaMoments: MetaMoments.getAllSleepMoments(),
  };
})(MetaMoment);
