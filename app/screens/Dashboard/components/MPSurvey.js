import React from 'react'
import { View, Text, TouchableOpacity, TextInput, Alert, ActivityIndicator } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import Meteor from 'react-native-meteor';
import Nucleo from '../../../components/icons/Nucleo';
import colors from '../../../config/colors.json';

import styles from './styles';
import _ from 'lodash';

const localStyle = {
  buttonView: {
    marginVertical: 10, 
    flexDirection: 'row', 
    alignItems: 'center',
  },
}

const initialState = {
  renderQuestion: true,
  renderSelections: false,
  happyQuestions: false,
  sadQuestions: false,
  saving: false,
  happyText: "",
  sadText: "",
  happy: false,
  soso: false,
  sad: false,
  happyUpNext: {
    active: false,
    color: colors.grayDark,
    icon: 'icon-circle'
  },
  happyOutlook: {
    active: false,
    color: colors.grayDark,
    icon: 'icon-circle'
  },
  happyEdu: {
    active: false,
    color: colors.grayDark,
    icon: 'icon-circle'
  },
  happyAnnouncements: {
    active: false,
    color: colors.grayDark,
    icon: 'icon-circle'
  },
  sadUpNext: {
    active: false,
    color: colors.grayDark,
    icon: 'icon-circle'
  },
  sadOutlook: {
    active: false,
    color: colors.grayDark,
    icon: 'icon-circle'
  },
  sadEdu: {
    active: false,
    color: colors.grayDark,
    icon: 'icon-circle'
  },
  sadAnnouncements: {
    active: false,
    color: colors.grayDark,
    icon: 'icon-circle'
  },
};

class MPSurvey extends React.Component {

  constructor(props) {
    super(props);
    this.state = { ...initialState };
  };

  submitFeedback = async () => {
    // meteor.call mpsurveyresults
    const theThis = this;
    this.setState({saving: true});
    const oem = await DeviceInfo.getManufacturer();
    const type = DeviceInfo.getDeviceType();
    const id = DeviceInfo.getDeviceId();
    const osVersion = DeviceInfo.getSystemVersion();
    const uniqueId = DeviceInfo.getUniqueId();
    const data = this.state;
    data.deviceInfo = {
      oem,
      type,
      id,
      osVersion,
      uniqueId
    };
    
    Meteor.call("saveMpSurvey", data, function(err, res) {
      if (err) {
        Alert.alert("Error", "We experienced an error saving your survey, please try again");
      } else {
        Alert.alert("Thanks!", "Your feedback submitted successfully")
      }
      theThis.setState(initialState);
    })
  }

  toggleSelected = (key) => {
    const obj = this.state[key];
    const active = !obj.active;
    const color = (active) ? colors.primaryA : colors.grayDark;
    const icon = (active) ? "icon-c-check" : "icon-circle";
    const newState = {};
    newState[key] = {
      active,
      color,
      icon
    }
    this.setState(newState);
  }

  render () {
    const { renderQuestion, renderSelections, happyQuestions, sadQuestions, happyText, sadText, saving } = this.state;
    return (
      <View style={styles.card}>
        <View style={styles.headerLabelView}>
          <Nucleo name="icon-survey" size={16} color={colors.primaryA} style={{marginRight:8}}/>
          <Text style={{fontWeight: 'bold', color: colors.primaryA}}>How are we doing?</Text>
        </View>
        {
          renderQuestion &&
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={{fontWeight: 'bold', fontSize: 24, color: colors.black}}>We made some changes to the home page! Are you enjoying those changes?</Text>
            <View style={{flex: 1, flexDirection: 'row', justifyContent: 'space-between', marginTop: 8, paddingHorizontal:36}}>
              <TouchableOpacity 
                onPress={() => this.setState({happy: true, renderQuestion: false, renderSelections: true, happyQuestions: true})} 
                style={localStyle.buttonView}
              >
                <Nucleo name="icon-smile" size={36} color={colors.primaryA} />
              </TouchableOpacity>
              <TouchableOpacity 
                onPress={() => this.setState({soso: true, renderQuestion: false, renderSelections: true, sadQuestions: true})} 
                style={localStyle.buttonView}
              >
                <Nucleo name="icon-speechless" size={36} color={colors.primaryA} />
              </TouchableOpacity>
              <TouchableOpacity 
                onPress={() => this.setState({sad: true, renderQuestion: false, renderSelections: true, sadQuestions: true})} 
                style={localStyle.buttonView}
              >
                <Nucleo name="icon-sad" size={36} color={colors.primaryA} />
              </TouchableOpacity>
            </View>
          </View>
        }
        {
          renderSelections && happyQuestions &&
          <View style={{flex: 1, flexDirection: 'column'}}>
              <Text style={{fontWeight: 'bold', fontSize: 24, color: colors.black}}>Great! Which changes do you like:</Text>
              <TouchableOpacity 
                onPress={() => this.toggleSelected("happyUpNext")} 
                style={localStyle.buttonView}
              >
                <Nucleo name={this.state.happyUpNext.icon} size={24} color={this.state.happyUpNext.color} />
                <Text style={{fontWeight: 'bold', color: this.state.happyUpNext.color, marginLeft: 16}}>Up-Next section</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                onPress={() => this.toggleSelected("happyOutlook")} 
                style={localStyle.buttonView}
              >
                <Nucleo name={this.state.happyOutlook.icon} size={24} color={this.state.happyOutlook.color} />
                <Text style={{fontWeight: 'bold', color: this.state.happyOutlook.color, marginLeft: 16}}>Outlook section</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                onPress={() => this.toggleSelected("happyEdu")} 
                style={localStyle.buttonView}
              >
                <Nucleo name={this.state.happyEdu.icon} size={24} color={this.state.happyEdu.color} />
                <Text style={{fontWeight: 'bold', color: this.state.happyEdu.color, marginLeft: 16}}>Express Drive-Up</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                onPress={() => this.toggleSelected("happyAnnouncements")} 
                style={localStyle.buttonView}
              >
                <Nucleo name={this.state.happyAnnouncements.icon} size={24} color={this.state.happyAnnouncements.color} />
                <Text style={{fontWeight: 'bold', color: this.state.happyAnnouncements.color, marginLeft: 16}}>Announcements</Text>
              </TouchableOpacity>

              <Text style={{fontWeight: 'bold', color: colors.primaryA, marginTop: 12}}>Anything Else?</Text>
              <TextInput 
                  multiline
                  numberOfLines={4}
                  ref={(input) => { this.inputRef = input}}
                  editable={true} 
                  style={{padding: 10, fontSize: 18, color: 'black', borderRadius: 4, borderColor: colors.primaryA, borderWidth: 1, marginVertical: 12}} 
                  value={happyText} 
                  onChangeText={(text) => this.setState({ happyText: text })} 
                />
          </View>
        }
        {
          renderSelections && sadQuestions &&
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={{fontWeight: 'bold', fontSize: 24, color: colors.black}}>What needs improvement?:</Text>
            <TouchableOpacity 
              onPress={() => this.toggleSelected("sadUpNext")} 
              style={localStyle.buttonView}
            >
              <Nucleo name={this.state.sadUpNext.icon} size={24} color={this.state.sadUpNext.color} />
              <Text style={{fontWeight: 'bold', color: this.state.sadUpNext.color, marginLeft: 16}}>Up-Next section</Text>
            </TouchableOpacity>
            <TouchableOpacity 
             onPress={() => this.toggleSelected("sadOutlook")} 
              style={localStyle.buttonView}
            >
              <Nucleo name={this.state.sadOutlook.icon} size={24} color={this.state.sadOutlook.color} />
              <Text style={{fontWeight: 'bold', color: this.state.sadOutlook.color, marginLeft: 16}}>Outlook section</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              onPress={() => this.toggleSelected("sadEdu")} 
              style={localStyle.buttonView}
            >
              <Nucleo name={this.state.sadEdu.icon} size={24} color={this.state.sadEdu.color} />
              <Text style={{fontWeight: 'bold', color: this.state.sadEdu.color, marginLeft: 16}}>Express Drive-Up</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              onPress={() => this.toggleSelected("sadAnnouncements")} 
              style={localStyle.buttonView}
            >
              <Nucleo name={this.state.sadAnnouncements.icon} size={24} color={this.state.sadAnnouncements.color} />
              <Text style={{fontWeight: 'bold', color: this.state.sadAnnouncements.color, marginLeft: 16}}>Announcements</Text>
            </TouchableOpacity>

            <Text style={{fontWeight: 'bold', color: colors.primaryA, marginTop: 12}}>Anything Else?</Text>
            <TextInput 
                ref={(input) => { this.inputRef = input}}
                editable={true}
                multiline
                numberOfLines={4}
                style={{padding: 10, fontSize: 18, color: 'black', borderRadius: 4, borderColor: colors.primaryA, borderWidth: 1, marginVertical: 12}} 
                value={sadText} 
                onChangeText={(text) => this.setState({ sadText: text })} 
            />
          </View>

        }
        {
          renderSelections && !saving &&
          <TouchableOpacity onPress={() => this.submitFeedback()} style={styles.button}>
            <Text style={styles.buttonText}>Submit Feedback</Text>
          </TouchableOpacity>
        }
        {
          renderSelections && saving &&
          <View style={styles.button}>
            <ActivityIndicator animating size="small" color={colors.white} />
          </View>
        }
      </View>
    )
  }
}

export default MPSurvey;