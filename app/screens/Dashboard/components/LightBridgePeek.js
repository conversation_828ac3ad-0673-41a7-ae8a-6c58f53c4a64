import React from 'react';
import {Text, View, Modal, Image, Platform, TouchableOpacity} from 'react-native';
import { Icon, Card, CardItem, Button, Left, Body, Container, Segment, StyleProvider, Right, Header, Title, Content} from 'native-base';
import Meteor, { withTracker } from 'react-native-meteor';
import getTheme from '../../../../native-base-theme/components';
import commonColor from '../../../../native-base-theme/variables/commonColor';
import sharedStyles from '../../../shared/SharedStyles';
import colors from '../../../config/colors.json';
import * as environmentSettings from '../../../shared/Settings';
import constants from '../../../config/constants.json';
import Nucleo from '../../../components/icons/Nucleo';
import _ from 'underscore';
import moment from 'moment-timezone';

import styles from './styles';

const colorRotation = [colors.secondary, colors.accent1, colors.primaryA];
const iconMap = {
	"Construction and Architecture": "icon-new-construction",
	"Discovery and Investigation": "icon-search",
	"Family Life and Community": "icon-meeting",
	"Math and Manipulatives": "icon-math",
	"Expression and Discovery": "icon-search",
	"Interactive Games and Dramatic Play": "icon-theater",
	"PM Activities": "icon-school",
	"Singing Sprouts / Music Appreciation": "icon-music",
	"Imagination and Expression": "icon-geometry",
	"Language and Literacy": "icon-book-08",
	"Outdoor Learning / Gym": "icon-athletics",
	"Science Lab": "icon-lab",
	"Signing Sprouts / Spanish Sprouts": "icon-handshake",
	"Signing Sprouts": "icon-handshake",
	"Social and Linguistic Learning": "icon-privacy-policy",
};

const getIcon = (tagTitle) => {
	let iconName = "icon-school";
	_.each(iconMap, (val, key) => {
		if (tagTitle.includes(key)) {
			iconName = val;
		}
	});
	return iconName;
}

// This is what happens on the server so applying the change here :-)
const formatStandardName = (name) => {
	return name.replace("`", " ");
}

function ThemeDetailModal( { visible, peekTheme, onClose }) {
	if (!peekTheme) return null;
	const standardGroupedActivities = _.groupBy( peekTheme.activities, (activity) => activity.selectedTypes[0]);
	let colorIndex = 0;
	return (
		<Modal
			animationType="slide"
        	transparent={false}
          	visible={visible}
        >
			<StyleProvider style={getTheme(commonColor)}>
				<Container>
					<Header style={{backgroundColor: colors.lighterGray}}>
						<Left>
						
						</Left>
						<Body style={{flex:2}}>
							<Title style={sharedStyles.headerTitleStyle}>Peek of the Week</Title>
						</Body>
						<Right>
							<Button transparent onPress={onClose}>
								<Text style={sharedStyles.headerTitleStyle}>Close</Text>
							</Button>
						</Right>
					</Header>
					<Content>
						{
							environmentSettings.PEEK_OF_THE_WEEK_IMGAE &&
							<Image source={require('../../../images/override/peek_of_the_week.png')} style={{height: 233, marginBottom: 6}}/>
						}
						<Card style={sharedStyles.cardStyle} key="headerCard">
							<CardItem bordered style={[sharedStyles.cardInnerStyle, {backgroundColor: colors.primaryA}]}>
								<Body>
									<Text style={{fontSize:18, color: colors.white, fontWeight: 'bold', paddingBottom:6, width:"100%", textAlign:"center"}}>{peekTheme.name}</Text>
									<Text style={{fontSize:14, color: colors.white, fontWeight: 'bold', paddingBottom:6, width:"100%", textAlign:"center"}}>{peekTheme.groupsLabel}</Text>
									<Text style={{fontSize:14, color: colors.white, fontWeight: 'bold', paddingBottom:6, width:"100%", textAlign:"center"}}>{peekTheme.spanLabel}</Text>
								</Body>
							</CardItem>
						</Card>
						{_.map(standardGroupedActivities, (activities, standardName) =>
							<Card style={sharedStyles.cardStyle} key="{{activity._id}}">
								<CardItem bordered style={[sharedStyles.cardInnerStyle, { backgroundColor: colorRotation[colorIndex++%3] }]}>
									<Body>
										<View style={{ flex:1, flexDirection: 'row' }}>
											<Nucleo name={getIcon(standardName)} size={24} color={colors.white}/>
											<Text style={{fontSize:18, fontWeight: 'bold', color: colors.white, marginHorizontal: 10}}>{formatStandardName(standardName)}</Text>
										</View>
									</Body>
								</CardItem>
								{activities.map( activity => 
								<CardItem bordered style={sharedStyles.cardInnerStyle}>
									<Body>
										<Text style={{fontSize:16, color: colors.black, paddingBottom:6}}>{activity.headline}</Text>
										{activity.message ? 
											<Text style={{fontSize:14, color: colors.black, paddingBottom:6}}>{activity.message}</Text>
										: null}
										{activity.teacherNotes ? 
											<Text style={{fontSize:14, color: colors.black, paddingBottom:6}}>{activity.teacherNotes}</Text>
										: null}
									</Body>
								</CardItem>
								)}
							</Card>
						)}
					</Content>
				</Container>
			</StyleProvider>
		</Modal>
	);
}


class LightBridgePeek extends React.PureComponent {
	constructor(props) {
		super(props);
		this.state = {
			peekData: null,
			retrievedOnce: false,
			themeDetailModalVisible: false
		}
	}

	_fetchData() {
		Meteor.call("lightbridgePeekData", (error, result) => {	
			this.setState({peekData:result, retrievedOnce: true});
		});
	}

	componentDidMount() {
		this._fetchData();
	}

	UNSAFE_componentWillReceiveProps(props) {
		const { refreshRequestAt } = this.props;
		if (props.refreshRequestAt !== refreshRequestAt) {
		  this._fetchData();
		}
	}

	showThemeDetailModal(peekTheme) {
		this.setState({
			themeDetailModalVisible: true,
			themeDetailModalTheme: peekTheme
		});
	}

	render() {
		if (!this.state.retrievedOnce) return null;
		
		_.each(this.state.peekData, (peekTheme) => {
			peekTheme.groupsLabel = _.map( peekTheme.groups, g => g.name).join(', '),
			peekTheme.spanLabel = new moment(peekTheme.startDay).format("M/D") + ' - ' + new moment(peekTheme.endDay).format("M/D")
		});

		return (
			<View style={styles.card}>
				<View style={styles.headerLabelView}>
					<Nucleo name="icon-presentation" size={16} color={colors.primaryA} style={{marginRight:8}}/>
					<Text style={{fontWeight: 'bold', color: colors.primaryA}}>Peek of the Week</Text>
				</View>
				{this.state.peekData && this.state.peekData.length > 0 ? 
					this.state.peekData.map( (peekTheme) => 
						<TouchableOpacity onPress={() => this.showThemeDetailModal(peekTheme)} style={{flexDirection: 'row', alignItems: 'center', paddingBottom: 16, marginBottom: 16, borderBottomWidth: 2, borderBottomColor: `${colors.black}1A`}}>
							<View style={{flexDirection: 'column'}}>
								<Text style={{color: `${colors.black}80`, fontSize:14, marginBottom: 8 }}>{peekTheme.spanLabel}</Text>
								<Text style={{fontSize: 20, fontWeight: 'bold', color: colors.black, marginBottom: 8 }}>{peekTheme.name}</Text>
								<Text style={{color: colors.black, fontSize:14}}>{peekTheme.groupsLabel}</Text>
							</View>
							<View style={{flex: 1}}/>
							<Nucleo name="icon-ctrl-right" size={24} color={colors.primaryA} style={{marginRight:8}}/>
						</TouchableOpacity>
				) : (
					<Text style={{ flex: 1, color: colors.black, fontSize:16, paddingBottom:6, textAlign:"center"}}>No peeks available for today. Stay tuned!</Text>
				)}
				<ThemeDetailModal 
					visible={this.state.themeDetailModalVisible}
					peekTheme={this.state.themeDetailModalTheme}
					onClose={() => {this.setState({ themeDetailModalVisible: false });}}
				/>
			</View>
		)
	}
}

export default LightBridgePeek;
