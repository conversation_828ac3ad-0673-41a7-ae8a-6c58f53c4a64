import React from 'react';
import { FlatList, Text, View, Dimensions, Image, TouchableWithoutFeedback, TouchableOpacity, Pressable, Modal, Alert, Linking, ScrollView, InteractionManager } from 'react-native';

import { FlatGrid } from 'react-native-super-grid';
import * as environmentSettings from '../../shared/Settings';
import Meteor, { withTracker } from 'react-native-meteor';
import LoadingScreen from '../../components/LoadingScreen';
import SyncManager from '../../shared/SyncManager';
import moment from 'moment';
import People from '../../api/People';
import DataValidation from '../../components/DataValidation';
import SyncStatus from '../../components/SyncStatus';
import Nucleo from '../../components/icons/Nucleo';
import { fetchSwitchableOrgs, fetchMemberships, fetchActiveAccounts } from '../../actions/userData'

import { CommonActions } from '@react-navigation/native';
import { connect } from 'react-redux';

import {
  LightBridgeTortal,
  StaffImpact,
  LightBridgePeek,
  MetaMoment,
  Outlook,
  MPSurvey,
  WhoAmI,
  CampaignList,
  TimeCardApproval
} from './components';
import colors from '../../config/colors.json';

import _ from 'underscore';
const syncContainer = SyncManager.getSyncContainer();
import styles from './components/styles';

const barWidth = Dimensions.get('window').width > 550 ? 450 : Dimensions.get('window').width - 100;
const width = Dimensions.get('window').width;

const momentIconName = (type) => {
  let name = "icon-c-info";
  switch (type) {
    case "food":
      name = "icon-cutlery-77";
      break;
    case "potty":
      name = "icon-toilet-paper";
      break;
    case "sleep":
      name = "icon-bedroom";
      break;
    case "activity":
      name = "icon-sport-mode";
      break;
    case "medical":
      name = "icon-hospital-32";
      break;
    case "mood":
      name = "icon-quite-happy";
      break;
    case "supplies":
      name = "icon-design";
      break;
    case "learning":
      name = "icon-presentation";
      break;
    case "portfolio":
      name = "icon-folder-check";
      break;
    case "incident":
      name = "icon-c-warning";
      break;
    case "illness":
      name = "icon-temperature-2";
      break;
    case "ouch":
      name = "icon-patch-34";
      break;
    case "comment":
      name = "icon-comment";
      break;
    case "move":
      name = "icon-swap-horizontal";
      break;
    case "checkin":
      name = "icon-c-check";
      break;
    case "checkout":
      name = "icon-leave";
      break;
    case "nameToFace":
      name = "icon-face-recognition";
      break;
  }
  return name;
}

const prefixOutlookQuery = [{"profileData.standardOutlook.allergies": {"$ne": "", "$exists": true}}, {"profileData.standardOutlook.importantNotes": {"$ne": "", "$exists": true}}, {"checkInOutlook": {"$exists": true}}];
const legacyOutlookQuery = [{"standardOutlook.allergies": {"$ne": "", "$exists": true}}, {"standardOutlook.importantNotes": {"$ne": "", "$exists": true}}, {"checkInOutlook": {"$exists": true}}];

class StaffDashboard extends React.PureComponent {

	constructor(props) {
		super(props);
		this.state = {
      momentSuggestions: [],
			showUploadProgress: false,
			refreshRequestAt: null,
			refreshing: false,
		};
		this._nodes = new Map();
    this._lastDataValidationId = null;
    this.interval = null;

    this.didFocusSubscription = this.props.navigation.addListener(
      'focus',
      () => {
        this._initiateFocusRefresh()
      }
    );
	}

  resetIntervalDetails = (passedDashboard = null) => {
    const dashboard = passedDashboard || this.props.dashboard;
    const now = new Date().valueOf();
    const suggestions = [];
    const momentSuggestions = dashboard?.momentSuggestions ?? [];
    for (const m of momentSuggestions ) {
      if (m.nextMomentDue) {
        if (now >= m.nextMomentDue) suggestions.push(m);
      } else {
        suggestions.push(m);
      }
    }

    this.setState({
      momentSuggestions: suggestions,
    })
  }

  resetInterval = (dashboard) => {
    if (this.interval) clearInterval(this.interval);
    this.interval = setInterval(this.resetIntervalDetails, 60000);
    this.resetIntervalDetails(dashboard);
  }

	UNSAFE_componentWillReceiveProps(nextProps) {
		const prevPerson = this.props.person, prevOrg = this.props.org;
		const nextPerson = nextProps.person, nextOrg = nextProps.org;
    const { dashboard } = nextProps;
    this.resetInterval(dashboard);

		if(prevPerson !== nextPerson  || prevOrg != nextOrg){
			const person = nextPerson || prevPerson, org = nextOrg || prevOrg;

      this.props.navigation.dispatch(CommonActions.setParams({
        showPinCodeCheckin: org?.hasCustomization("people/pinCodeCheckin/enabled") && (person?.type ==="admin" || (!(org?.hasCustomization("people/kioskMasterPinAdminOnly/enabled")) && person?.type ==="staff")),
				userPerson: person,
      }));
		}
	}

	componentDidMount() {
    const {
      dashboard,
      fetchMemberships,
      fetchSwitchableOrgs,
      fetchActiveAccounts,
    } = this.props;
    const user = Meteor.user();
    if (user) {
      try {
        fetchActiveAccounts();
      } catch (error) {
        console.log('error fetching active accounts', error);
      }
      try {
        fetchMemberships();
      } catch (error) {
        console.log('error fetching memberships', error);
      }
      try {
        fetchSwitchableOrgs();
      } catch (error) {
        console.log('error fetching switchable orgs', error);
      }
    }
    this.resetInterval(dashboard);
    this.props.navigation.dispatch(CommonActions.setParams({
      showPinCodeCheckin: this.props.org && this.props.org.hasCustomization("people/pinCodeCheckin/enabled") && this.props.person && (this.props.person.type=="admin" || (!(this.props.org.hasCustomization("people/kioskMasterPinAdminOnly/enabled")) && this.props.person.type=="staff")),
      userPerson: this.props.person
    }));
	}

  componentWillUnmount() {
    if (this.interval) clearInterval(this.interval);

    try {
      this.didFocusSubscription();
    } catch(e) {
      console.log("error with subscription remove staff dashboard", e)
    }
  }


	_loadMoreMoments = () => {
		const newMomentCount = this.state.momentCount + 20
		this.setState({momentCount: newMomentCount});
	}

	handleRestartDownloads = () => SyncManager.restartDownloads();
	handleCancelDownloads = () => SyncManager.cancelDownloads();

  onDataValidationPress = (dv) => {
    const { person, currentOrg } = this.props;
    this._lastDataValidationId = dv._id;
    this.props.navigation.navigate('DataValidationDetail', { dataValidation: dv, currentPerson: person, currentOrg, });
  }

	_initiateRefresh = () => {
		const refreshTimestamp = new moment().valueOf();
		this.setState({refreshRequestAt: refreshTimestamp, refreshing: false});
	}

  _initiateFocusRefresh = () => {
    if (this._lastDataValidationId != null) {
      this._lastDataValidationId = null;
      this._initiateRefresh();
    }
  }

  launchEduModal = () => {
    const { navigation, dashboard } = this.props;
    navigation.navigate("ExpressDriveUpModal", { ...dashboard.edu });
  }

  launchAnnouncementModal = () => {
    const { navigation, announcements } = this.props;
    navigation.navigate("AnnouncementsListModal", { announcements });
  }

  launchMomentSuggestionModal = (params) => {
    const { person, org, navigation } = this.props;
    navigation.navigate("MomentSuggestionModal", {...params, person, currentOrg: org});
  }

  launchMomentModal = () => {
    const { navigation, person } = this.props;
    navigation.navigate('MomentListModal', { person });
  }

  launchOutlookModal = (outlookPeople) => {
    const { navigation, person } = this.props;
    checkInGroup = Meteor.collection('groups').findOne({_id: person.checkInGroupId});
    navigation.navigate('OutlookModal', {people: outlookPeople, checkInGroup });
  }

  renderMomentSuggestion = ({item}) => {
    const count = item?.data?.length || item?.count || 0;
    return (
      <Pressable
        key={`${item.type}_card`}
        onPress={() => this.launchMomentSuggestionModal({people: item.data, type: item.type, title: item.prettyName})}
        style={({ pressed }) => [
          styles.card,
          {
            marginHorizontal: 0,
            marginVertical: 0,
            backgroundColor: pressed
              ? `${colors.primaryA}1A`
              : colors.white
          },
        ]}
      >
        <View style={[styles.headerLabelView, {marginBottom: 16, justifyContent: 'space-between'}]}>
          <Nucleo name={momentIconName(item.type)} size={24} color={colors.primaryA} />
          <View style={{width: 24, height: 24, borderRadius: 12, backgroundColor: colors.red, justifyContent: 'center', alignItems:'center' }}>
              <Text style={{fontWeight: 'bold', color: colors.white}}>{count}</Text>
          </View>
        </View>
        <Text style={{fontWeight: 'bold', color: colors.black, fontSize: 16}}>{item.prettyName}</Text>
      </Pressable>
    )
  }

	render() {
    const { dashboard, org, person, subsLoading, announcements  } = this.props;
    const { momentSuggestions } = this.state;
		if ( subsLoading ) {
      return ( <LoadingScreen /> )
    }
    const hasSuggestions = momentSuggestions?.length > 0;
    const hasAnnouncements = announcements?.length > 0;
    const hasArrivals = dashboard?.edu?.arrivals?.length > 0;
    const hasDepartures = dashboard?.edu?.departures?.length > 0;
    const hasHere = dashboard?.edu?.here?.length > 0;
    const hasEdu = hasArrivals || hasDepartures || hasHere;
    const suggestions = momentSuggestions ?? [];
    const lbCustomCards = org?.customizations?.["customer/lightbridge/default"] == true;
    const timeCardEnable = org?.customizations?.["people/timeConfirmation/enabled"] == true;

    let outlookPeople = [];
    let hasMpSurvey = false;
    if (org) {
      const outlookQuery = { type: 'person', checkInGroupId: person.checkInGroupId };
      if (org.profileDataPrefix()) {
        outlookQuery["$or"] = prefixOutlookQuery;
      } else {
        outlookQuery["$or"] = legacyOutlookQuery;
      }
      outlookPeople = People.find(outlookQuery);

      hasMpSurvey = org.hasCustomization("mpsurvey/enabled");
    }
    const hasOutlook = outlookPeople?.length > 0;

    return (
      <ScrollView testID='dashboard' style={styles.list}>
       <WhoAmI person={person} props = {this.props}/>
        <SyncStatus
          syncContainer={syncContainer}
          handleRestartDownloads={this.handleRestartDownloads}
          handleCancelDownloads={this.handleCancelDownloads}
        />
        {
          hasMpSurvey &&
          <MPSurvey />
        }
        <CampaignList userPerson={person}/>
        { timeCardEnable && <TimeCardApproval />}
        <MetaMoment person={person} />
        {
          hasSuggestions &&
          <Text style={{color: colors.primaryA, fontWeight: 'bold', fontSize: 18, marginTop: 16, marginBottom: 4, marginLeft: 16}}>Up-Next</Text>
        }
        <ScrollView scrollEnabled={false} horizontal={true} contentContainerStyle={{width:'100%'}}>
          <FlatGrid
            scrollEnabled={false}
            keyboardShouldPersistTaps='handled'
            itemDimension={(width > 800) ? 160 : 130}
            keyExtractor={(item, index) => item.type}
            data={suggestions}
            renderItem={this.renderMomentSuggestion}
            style={{marginLeft: 8, marginRight: 8}}
          />
        </ScrollView>
        {
          hasEdu &&
          <Pressable style={styles.card}
            key={`edu_card`}
            onPress={() => this.launchEduModal()}
            style={({ pressed }) => [
              styles.card,
              {
                backgroundColor: pressed
                  ? `${colors.primaryA}1A`
                  : colors.white
              },
            ]}
          >
            <View style={[styles.headerLabelView, {flexDirection: 'column',marginBottom: 0, alignItems: 'flex-start', justifyContent: 'space-between'}]}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Nucleo name="icon-r-chat" size={24} color={colors.primaryA} />
                <Text style={{fontWeight: 'bold', color: colors.black, marginLeft: 16, fontSize: 16}}>{environmentSettings.EDU_CARD_TITLE}</Text>
                <View style={{flex:1}}></View>
                <Nucleo name="icon-ctrl-right" size={24} color={colors.primaryA} />
              </View>
              {
                hasArrivals &&
                <Text style={{color: `${colors.black}80`, fontSize: 14, fontWeight: 'bold', marginTop: 8}}>{`${dashboard.edu.arrivals.length} Arriving Soon`}</Text>
              }
              {
                hasDepartures &&
                <Text style={{color: `${colors.black}80`, fontSize: 14, fontWeight: 'bold', marginTop: 8}}>{`${dashboard.edu.departures.length} Leaving Soon`}</Text>
              }
              {
                hasHere &&
                <Text style={{color: `${colors.black}80`, fontSize: 14, fontWeight: 'bold', marginTop: 8}}>{`${dashboard.edu.here.length} Here`}</Text>
              }
            </View>
          </Pressable>

        }
        {
          hasOutlook &&
          <Pressable style={styles.card}
            key={`outlook_card`}
            onPress={() => this.launchOutlookModal(outlookPeople)}
            style={({ pressed }) => [
              styles.card,
              {
                backgroundColor: pressed
                  ? `${colors.primaryA}1A`
                  : colors.white
              },
            ]}
          >
            <View style={[styles.headerLabelView, {marginBottom: 0, justifyContent: 'space-between'}]}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Nucleo name="icon-hospital-32" size={24} color={colors.primaryA} />
                <Text style={{fontWeight: 'bold', color: colors.black, marginLeft: 16, fontSize: 16}}>Outlook</Text>
              </View>
              <Nucleo name="icon-ctrl-right" size={24} color={colors.primaryA} />
            </View>
          </Pressable>
        }
        <DataValidation
          onDataValidationPress={this.onDataValidationPress}
          refreshRequestAt={this.state.refreshRequestAt}
        />
        {
          hasAnnouncements &&
          <Pressable
            key={`announcements_card`}
            onPress={() => this.launchAnnouncementModal()}
            style={({ pressed }) => [
              styles.card,
              {
                backgroundColor: pressed
                  ? `${colors.primaryA}1A`
                  : colors.white
              },
            ]}
            >
            <View style={[styles.headerLabelView, {marginBottom: 0, justifyContent: 'space-between'}]}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Nucleo name="icon-megaphone" size={24} color={colors.primaryA} />
                <Text style={{fontWeight: 'bold', color: colors.black, marginLeft: 16, fontSize: 16}}>Announcements</Text>
              </View>
              <Nucleo name="icon-ctrl-right" size={24} color={colors.primaryA} />
            </View>
          </Pressable>
        }
        <Pressable
          key={`moments_card`}
          testID={"momentsCard"}
          onPress={() => this.launchMomentModal()}
          style={({ pressed }) => [
            styles.card,
            {
              backgroundColor: pressed
                ? `${colors.primaryA}1A`
                : colors.white
            },
          ]}
          >
          <View style={[styles.headerLabelView, {marginBottom: 0, justifyContent: 'space-between'}]}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Nucleo name="icon-contacts-45" size={24} color={colors.primaryA} />
              <Text style={{fontWeight: 'bold', color: colors.black, marginLeft: 16, fontSize: 16}}>Moments</Text>
            </View>
            <Nucleo name="icon-ctrl-right" size={24} color={colors.primaryA} />
          </View>
        </Pressable>
        {
          lbCustomCards &&
          <LightBridgeTortal />
        }
        {
          lbCustomCards &&
          <LightBridgePeek refreshRequestAt={this.state.refreshRequestAt}/>
        }
        <StaffImpact refreshRequestAt={this.state.refreshRequestAt} />
      </ScrollView>
    )
	}
}
const mapDispatchToProps =  {
  fetchSwitchableOrgs,
  fetchMemberships,
  fetchActiveAccounts,
};

export default withTracker(params => {
  const user = Meteor.user();
	const org = Orgs.findOne({_id: user?.orgId});
	const	person = People.findOne({_id: user?.personId});

	// if the userPerson group changes we're sending that groupId in the params to reactively update the subscription
	// the server side subscription ignores this variable
	const groupId = (person) ? (person.checkInGroupId || person.defaultGroupId) : null;
  const dashboardHandle = Meteor.subscribe("theGroupDashboard", { groupId });

	return {
		subsLoading: !dashboardHandle.ready(),
		announcements: Meteor.collection('announcements').find(),
		person,
		org,
    dashboard: Meteor.collection('groupDashboards').findOne({}),
	};
})(connect(null, mapDispatchToProps)(StaffDashboard));
