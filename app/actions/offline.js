import _ from 'lodash';
import { Alert } from 'react-native';
import Meteor from 'react-native-meteor';
import * as environmentSettings from '../shared/Settings';
import Random from '../shared/Random';
import moment from 'moment-timezone';
import Orgs from '../api/Orgs';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  OFFLINE_ADD_SLEEP_MOMENTS,
  OFFLINE_SLEEP_CHECK,
  OFFLINE_END_SLEEP,
  OFFLINE_NEW_MOMENT,
  OFFLINE_SYNC_MOMENTS,
  OFFLINE_START_SYNC_MOMENTS,
  OFFLINE_END_SYNC_MOMENTS,
  AUTH_CLEAR_DATA,
  OFFLINE_CHECKIN,
  OFFLINE_CHECKIN_SUCCESS,
  OFFLINE_CHECKIN_ERROR,
  OFFLINE_UPDATE_PEOPLE, OFFLINE_CLEAR_MOMENTS,
} from '../utility/actions';

import { setOptions, fetchAndParse } from '../utility/httpFetch';




  // Configuration for moment types and their specific fields to be extracted
  const momentFieldExtractionConfig = {
    sleep: [
      'sleepDidNotSleep', 'sleepSatQuietly', 'endTime', 'time', 'sleepChecks', 'sleepLastSleepCheck'
    ],
    potty: [
      'pottyType', 'pottyTypeContinence', 'pottyTraining', 'pottyAppliedOintment'
    ],
    food: [
      'foodType','foodAmount'
    ]
  };




export const addSleepMoments = (taggedPeople = []) => {
  return (dispatch, getState) => {
    console.log('addSleepMoments called with taggedPeople:', taggedPeople);
    const { offlinePeople, offlineGroups } = getState().auth;
    const currentOrg = getState().auth.currentOrg;
    const newMoments = [];

    for (const item of taggedPeople) {
      // Handle both object format from Tagger component and simple ID strings
      const id = typeof item === 'string' ? item : item.tagId;
      const person = _.find(offlinePeople, {_id: id});
      const now = new moment();
      if (person) {
        const sleepMomentData = {
          personId: person._id,
          taggedPeople: [person._id],
          currentOrg,
          date: now.toDate(),
          time: now.toDate(),
          createdAt: now.valueOf(),
          momentFieldValues: {
            sleepDidNotSleep: false,
            sleepSatQuietly: false,
            originalCreatedAt: now.valueOf(), // Store for AsyncStorage updates
          }
        };
        console.log('Created sleep moment data:', sleepMomentData);
        dispatch(saveMoment('sleep', sleepMomentData));

        // Create the moment object for the Redux state
        const newMoment = {
          _id: Random.id(),
          firstName: person.firstName,
          lastName: person.lastName,
          personId: person._id,
          createdAt: now.valueOf(),
          sleepCheckInterval: null,
          offlineType: 'sleep',
          time: now.format("h:mm a"),
          date: now.format("MM/DD/YYYY"),
          momentType: "sleep",
        };
        newMoments.push(newMoment);
      } else {
        console.log('Person not found in offlinePeople for id:', id);
      }
    }

    console.log('Dispatching OFFLINE_ADD_SLEEP_MOMENTS with newMoments:', newMoments);
    dispatch({ type: OFFLINE_ADD_SLEEP_MOMENTS, payload: { newMoments } });
  }
};

export const sleepCheck = (_id, opts = {}) => {
  return async (dispatch, getState) => {
    const { sleepMoments } = getState().offline;
    const { personInfo } = getState().auth;
    const sleepMoment = _.find(sleepMoments, { _id });
    if (!sleepMoment) return;

    const createdAt = new Date().valueOf();
    const sleepCheck = {
      personId: sleepMoment.personId,
      createdAt,
      createdBy: personInfo?._id ?? null,
    }

    if (opts.sleepPosition) {
      sleepCheck.sleepPosition = opts.sleepPosition
    }

    if (opts.distressedSleep) {
      sleepCheck.distressedSleep = opts.distressedSleep
    }
    const sleepChecks = sleepMoment?.sleepChecks ?? [];
    sleepChecks.push(sleepCheck);
    sleepMoment.sleepLastSleepCheck = createdAt
    sleepMoment.sleepChecks = sleepChecks;

    // Update Redux state
    dispatch({ type: OFFLINE_SLEEP_CHECK, payload: { sleepMoment }});

    // Also update the moment in AsyncStorage
    const updates = {
      momentFieldValues: {
        ...sleepMoment.momentFieldValues,
        sleepChecks: sleepChecks,
        sleepLastSleepCheck: createdAt
      }
    };
    await updateOfflineMomentInStorage(sleepMoment.createdAt, updates);
  }
}

export const endSleep = (_id, opts = {}) => {
  return async (dispatch, getState) => {
    const { sleepMoments } = getState().offline;
    const { personInfo } = getState().auth;
    const currentOrg = getState().auth.currentOrg;
    const sleepMoment = _.find(sleepMoments, { _id });
    if (!sleepMoment) return;

    // Update Redux state
    const newSleepMoment = {...sleepMoment, ...opts};
    dispatch({ type: OFFLINE_END_SLEEP, payload: { sleepMoment: newSleepMoment }});

    // Get the current moment from AsyncStorage to preserve sleep checks
    const momentsJson = await AsyncStorage.getItem('offline_moments');
    const moments = momentsJson ? JSON.parse(momentsJson) : [];
    const storedMoment = moments.find(m => m.createdAt === sleepMoment.createdAt);

    // Get sleep checks from stored moment or Redux state
    const currentSleepChecks = storedMoment?.momentFieldValues?.sleepChecks || sleepMoment.sleepChecks || [];

    // Update the existing moment in AsyncStorage with end sleep data
    const updates = {
      momentFieldValues: {
        ...sleepMoment.momentFieldValues,
        ...(storedMoment?.momentFieldValues || {}),
        time: sleepMoment.time,
        endTime: opts.endTime,
        sleepDidNotSleep: opts.sleepDidNotSleep || false,
        sleepSatQuietly: opts.sleepSatQuietly || false,
        sleepChecks: currentSleepChecks,
        comment: opts.comment || ''
      }
    };

    console.log('Updating sleep moment in AsyncStorage with end sleep data:', updates);
    console.log('Current sleep checks being preserved:', currentSleepChecks);
    await updateOfflineMomentInStorage(sleepMoment.createdAt, updates);
  }
}

export const nameToFace = (taggedPeople) => {
  return (dispatch, getState) => {
    const { personInfo, orgInfo } = getState().auth;
    const { cachedPeople } = getState().offline;

    console.log('nameToFace action received taggedPeople:', JSON.stringify(taggedPeople, null, 2));

    // Use the helper function to create the moment object
    const { createNameToFaceMoment } = require('../screens/Offline/offlineHelpers');
    const newMoment = createNameToFaceMoment(taggedPeople, personInfo, orgInfo, cachedPeople);

    // Log the offlineData to verify it's correctly formatted
    console.log('Creating offline nameToFace moment with offlineData:', JSON.stringify(newMoment.offlineData, null, 2));

    // Store the moment in Redux for later syncing
    dispatch({ type: OFFLINE_NEW_MOMENT, payload: { newMoment }});

    // Also save to AsyncStorage for persistence across app restarts
    saveOfflineMomentToStorage(newMoment);
  };
}

// Helper function to save offline moments to AsyncStorage
const saveOfflineMomentToStorage = async (moment) => {
  try {
    // Get existing moments from storage
    const momentsJson = await AsyncStorage.getItem('offline_moments');
    const moments = momentsJson ? JSON.parse(momentsJson) : [];

    // Add the new moment
    moments.push(moment);

    // Save back to storage
    await AsyncStorage.setItem('offline_moments', JSON.stringify(moments));
    console.log('Saved offline moment to AsyncStorage');
  } catch (error) {
    console.error('Error saving offline moment to AsyncStorage:', error);
  }
};

// Helper function to update an existing moment in AsyncStorage
const updateOfflineMomentInStorage = async (momentId, updates) => {
  try {
    // Get existing moments from storage
    const momentsJson = await AsyncStorage.getItem('offline_moments');
    const moments = momentsJson ? JSON.parse(momentsJson) : [];

    // Find and update the moment
    const momentIndex = moments.findIndex(m =>
      m.personId === momentId ||
      m.createdAt === momentId ||
      (m.momentFieldValues && m.momentFieldValues.originalCreatedAt === momentId)
    );

    if (momentIndex !== -1) {
      // Update the moment with new data
      moments[momentIndex] = { ...moments[momentIndex], ...updates };

      // Save back to storage
      await AsyncStorage.setItem('offline_moments', JSON.stringify(moments));
      console.log('Updated offline moment in AsyncStorage');
      return true;
    } else {
      console.log('Moment not found in AsyncStorage for update');
      return false;
    }
  } catch (error) {
    console.error('Error updating offline moment in AsyncStorage:', error);
    return false;
  }
};

export const saveMoment = (name, data) => {
  return (dispatch, getState) => {
    let formattedTaggedPeople
    if (data.momentType === 'sleep') {
      formattedTaggedPeople =
        data.taggedPeople?.map(personId => {
          // If it's already in the correct format, keep it as is
          if (typeof personId === 'string' && personId.includes('|')) {
            return personId;
          }
          // Otherwise, format it as "person|{personId}"
          return `person|${personId}`;
        }) || [];
    } else {
      formattedTaggedPeople = data.taggedPeople || [];
    }

    const newMoment = {
      personId: data.personId,
      offlineType: name,
      momentType: name,
      taggedPeople: formattedTaggedPeople,
      date: moment(data.date).format("MM/DD/YYYY"),
      time: moment(data.time).format("h:mm a"),
      prettyTime: moment(data.time).format("h:mm a"),
      momentFieldValues: data.momentFieldValues,
      comment: data.momentFieldValues?.comment,
      createdAt: data.createdAt,
      currentUser: getState().auth.userInfo,
      currentPerson: getState().auth.personInfo,
      currentOrg: data.currentOrg,
      attachedMedia: data.attachedMedia,
    };

    if (name === 'checkOut') {
      newMoment.mood = data.momentFieldValues?.mood;
      newMoment.checkedOutById = data.momentFieldValues?.checkedOutById;

      if (data.currentOrg && data.currentOrg._id) {
        newMoment.orgId = data.currentOrg._id;
      } else if (getState().auth.orgInfo && getState().auth.orgInfo._id) {
        newMoment.orgId = getState().auth.orgInfo._id;
      }
    } else if (name === 'checkIn') {
      newMoment.checkInTime = data.timestamp || data.checkInTime;
      newMoment.checkInGroupId = data.groupId;
      newMoment.groupName = data.groupName;
      newMoment.checkedInById = getState().auth.personInfo?._id ?? null;
      newMoment.prettyTime = data.prettyTime;
      newMoment.comments = data.comments; // Server expects 'comments' field

      if (data.currentOrg && data.currentOrg._id) {
        newMoment.orgId = data.currentOrg._id;
      } else if (getState().auth.orgInfo && getState().auth.orgInfo._id) {
        newMoment.orgId = getState().auth.orgInfo._id;
      }

      // For individual check-ins, taggedPeople should be an array with the single personId
      if (data.personId) {
        newMoment.taggedPeople = [data.personId];
      }

      // If we have selectedChildren, use them as taggedPeople (for backward compatibility)
      if (data.selectedChildren && data.selectedChildren.length > 0) {
        newMoment.taggedPeople = data.selectedChildren;
      }
    }

    console.log(`Saving ${name} moment to Redux store with comment ${newMoment.comment}:`, newMoment);
    dispatch({ type: OFFLINE_NEW_MOMENT, payload: { newMoment }});

    // Also save to AsyncStorage for persistence across app restarts
    saveOfflineMomentToStorage(newMoment);
  }
}

// Helper function to get stored moments from AsyncStorage
const getStoredMoments = async (key) => {
  try {
    const data = await AsyncStorage.getItem(key);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error(`Error retrieving stored moments from ${key}:`, error);
    return [];
  }
};

// Helper function to normalize Redux moments to a consistent format
const normalizeReduxMoment = (moment) => {
  return {
    personId: moment.personId,
    offlineType: moment.offlineType,
    momentType: moment.momentType,
    createdAt: moment.createdAt,
    comment: moment.comment,
    prettyTime: moment.prettyTime,
    orgId: moment.currentOrg?._id || moment.orgId,
    groupId: moment.groupId,
    groupName: moment.groupName,
    checkedInById: moment.checkedInById,
    checkedOutById: moment.checkedOutById,
    mood: moment.mood,
    originalMoment: moment
  };
};

// Helper function to normalize AsyncStorage check-in moments to a consistent format
const normalizeAsyncStorageCheckin = (checkin) => {
  return {
    personId: checkin._id || checkin.personId,
    offlineType: 'checkIn',
    momentType: 'checkIn',
    createdAt: checkin.checkInTime || checkin.createdAt,
    comment: checkin.comment,
    prettyTime: moment(checkin.checkInTime || checkin.createdAt).format("h:mm a"),
    orgId: checkin.orgId,
    groupId: checkin.checkInGroupId,
    groupName: checkin.groupName,
    checkedInById: checkin.checkedInById,
    originalMoment: checkin
  };
};

// Helper function to normalize AsyncStorage check-out moments to a consistent format
const normalizeAsyncStorageCheckout = (checkout) => {
  return {
    personId: checkout._id || checkout.personId,
    offlineType: 'checkOut',
    momentType: 'checkOut',
    createdAt: checkout.checkOutTime || checkout.createdAt,
    comment: checkout.checkOutData?.comment || checkout.comment,
    prettyTime: moment(checkout.checkOutTime || checkout.createdAt).format("h:mm a"),
    orgId: checkout.orgId,
    mood: checkout.checkOutData?.mood || checkout.mood,
    checkedOutById: checkout.checkedOutById,
    originalMoment: checkout
  };
};

// Helper function to deduplicate moments based on personId and timestamp
const deduplicateMoments = (moments) => {
  const seen = new Set();
  return moments.filter(moment => {
    const key = `${moment.personId}-${moment.createdAt}`;
    if (seen.has(key)) {
      console.log(`Removing duplicate moment for person ${moment.personId} at ${moment.createdAt}`);
      return false;
    }
    seen.add(key);
    return true;
  });
};

// Helper function to process check-in and check-out moments sequentially in chronological order
const processCheckInOutMomentsSequentially = async (moments) => {
  if (moments.length === 0) {
    console.log('No check-in/check-out moments to process');
    return { errors: 0, processed: 0 };
  }

  console.log(`Processing ${moments.length} check-in/check-out moments sequentially`);
  let totalErrors = 0;

  // Process moments one by one in chronological order
  for (const moment of moments) {
    try {
      console.log(`Syncing ${moment.offlineType} moment for person ${moment.personId} at ${moment.prettyTime || new Date(moment.createdAt).toLocaleTimeString()}`);

      if (moment.offlineType === 'checkIn') {
        // Create data for check-in server method
        const checkinData = {
          personId: moment.personId,
          offlineType: moment.offlineType,
          momentType: moment.momentType,
          createdAt: moment.createdAt,
          prettyTime: moment.prettyTime,
          comments: moment.comment, // Server expects 'comments' not 'comment'
          orgId: moment.orgId,
          groupId: moment.groupId,
          groupName: moment.groupName,
          checkedInById: moment.checkedInById,
          // Flag to skip race condition handling during offline sync
          skipRaceConditionHandling: true,
          // Include any additional fields from the original moment
          ...moment.originalMoment
        };

        // Ensure orgId is set
        if (!checkinData.orgId && moment.originalMoment?.currentOrg?._id) {
          checkinData.orgId = moment.originalMoment.currentOrg._id;
          console.log("Added orgId from currentOrg:", checkinData.orgId);
        }

        await new Promise((resolve) => {
          Meteor.call('checkIn', checkinData, (error, result) => {
            if (error) {
              // If the person is already checked in, consider it a success
              if (error.reason === "Person already checked in") {
                console.log('Person already checked in on server:', moment.personId);
                resolve(true);
                return;
              }

              console.log('Error syncing check-in moment:', error);
              console.log('Error details:', error.details);
              console.log('Error reason:', error.reason);
              totalErrors++;
              resolve(false);
            } else {
              console.log('Check-in moment synced successfully');
              resolve(true);
            }
          });
        });

      } else if (moment.offlineType === 'checkOut') {
        // Create data for check-out server method
        const checkoutData = {
          personId: moment.personId,
          offlineType: moment.offlineType,
          momentType: moment.momentType,
          createdAt: moment.createdAt,
          prettyTime: moment.prettyTime,
          comments: moment.comment, // Server expects 'comments' not 'comment'
          mood: moment.mood,
          checkedOutById: moment.checkedOutById,
          orgId: moment.orgId,
          // Flag to skip race condition handling during offline sync
          skipRaceConditionHandling: true,
          // Include any additional fields from the original moment
          ...moment.originalMoment
        };

        await new Promise((resolve) => {
          Meteor.call('checkOut', checkoutData, (error, result) => {
            if (error) {
              console.log('Error syncing check-out moment:', error);
              console.log('Error details:', error.details);
              console.log('Error reason:', error.reason);
              totalErrors++;
              resolve(false);
            } else {
              console.log('Check-out moment synced successfully');
              resolve(true);
            }
          });
        });
      }

      // Add a small delay between requests to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
      console.error('Unexpected error processing moment:', error);
      totalErrors++;
    }
  }

  console.log(`Sequential check-in/out sync completed: ${moments.length - totalErrors} successful, ${totalErrors} failed`);

  // After all moments are synced, run race condition handling for each person
  if (totalErrors === 0) {
    console.log('Running race condition handling after offline sync...');
    const uniquePersonIds = [...new Set(moments.map(m => m.personId))];

    for (const personId of uniquePersonIds) {
      try {
        // Run race condition handling for this person
        await new Promise((resolve) => {
          Meteor.call('resolveRaceConditionsForPerson', {
            personId,
            startDate: new Date(new Date().setHours(0, 0, 0, 0)) // Today only
          }, (error, result) => {
            if (error) {
              console.log(`Error resolving race conditions for person ${personId}:`, error);
            } else {
              console.log(`Race conditions resolved for person ${personId}:`, result);
            }
            resolve();
          });
        });
      } catch (error) {
        console.error(`Failed to resolve race conditions for person ${personId}:`, error);
      }
    }
  }

  return { errors: totalErrors, processed: moments.length };
};

// Helper function to process check-in moments
const processCheckInMoments = async (checkinMoments) => {
  if (checkinMoments.length === 0) {
    console.log('No check-in moments to process');
    return { errors: 0, processed: 0 };
  }

  console.log(`Processing ${checkinMoments.length} check-in moments`);
  let checkinErrors = 0;

  const promises = checkinMoments.map(moment => {
    return new Promise((resolve) => {
      console.log('Syncing check-in moment:', JSON.stringify({
        personId: moment.personId,
        createdAt: moment.createdAt,
        comment: moment.comment,
        orgId: moment.orgId,
      }));

      // Create data for server method
      const checkinData = {
        personId: moment.personId,
        offlineType: moment.offlineType,
        momentType: moment.momentType,
        createdAt: moment.createdAt,
        prettyTime: moment.prettyTime,
        comments: moment.comment, // Server expects 'comments' not 'comment'
        orgId: moment.orgId,
        groupId: moment.groupId,
        groupName: moment.groupName,
        checkedInById: moment.checkedInById,
        // Include any additional fields from the original moment
        ...moment.originalMoment
      };

      // Ensure orgId is set
      if (!checkinData.orgId && moment.originalMoment?.currentOrg?._id) {
        checkinData.orgId = moment.originalMoment.currentOrg._id;
        console.log("Added orgId from currentOrg:", checkinData.orgId);
      }

      Meteor.call('checkIn', checkinData, (error, result) => {
        if (error) {
          // If the person is already checked in, consider it a success
          if (error.reason === "Person already checked in") {
            console.log('Person already checked in on server:', moment.personId);
            resolve(true);
            return;
          }

          console.log('Error syncing check-in moment:', error);
          console.log('Error details:', error.details);
          console.log('Error reason:', error.reason);
          checkinErrors++;
          resolve(false);
        } else {
          console.log('Check-in moment synced successfully');
          resolve(true);
        }
      });
    });
  });

  await Promise.all(promises);
  console.log(`Check-in sync completed: ${checkinMoments.length - checkinErrors} successful, ${checkinErrors} failed`);

  return { errors: checkinErrors, processed: checkinMoments.length };
};

// Helper function to process check-out moments
const processCheckOutMoments = async (checkoutMoments) => {
  if (checkoutMoments.length === 0) {
    console.log('No check-out moments to process');
    return { errors: 0, processed: 0 };
  }

  console.log(`Processing ${checkoutMoments.length} check-out moments`);
  let checkoutErrors = 0;

  const promises = checkoutMoments.map(moment => {
    return new Promise((resolve) => {
      console.log('Syncing check-out moment:', JSON.stringify({
        personId: moment.personId,
        createdAt: moment.createdAt,
        comment: moment.comment,
        mood: moment.mood,
      }));

      // Create data for server method
      const checkoutData = {
        personId: moment.personId,
        offlineType: moment.offlineType,
        momentType: moment.momentType,
        createdAt: moment.createdAt,
        prettyTime: moment.prettyTime,
        comments: moment.comment, // Server expects 'comments' not 'comment'
        mood: moment.mood,
        checkedOutById: moment.checkedOutById,
        orgId: moment.orgId,
        // Include any additional fields from the original moment
        ...moment.originalMoment
      };

      Meteor.call('checkOut', checkoutData, (error, result) => {
        if (error) {
          console.log('Error syncing check-out moment:', error);
          console.log('Error details:', error.details);
          console.log('Error reason:', error.reason);
          checkoutErrors++;
          resolve(false);
        } else {
          console.log('Check-out moment synced successfully');
          resolve(true);
        }
      });
    });
  });

  await Promise.all(promises);
  console.log(`Check-out sync completed: ${checkoutMoments.length - checkoutErrors} successful, ${checkoutErrors} failed`);

  return { errors: checkoutErrors, processed: checkoutMoments.length };
};

export const syncMoments = () => {
  return async (dispatch, getState) => {
    console.log("Starting moment sync");
    dispatch({ type: OFFLINE_START_SYNC_MOMENTS, payload: { momentsSyncing: true } });
    const { resumeToken } = getState().auth;
    const { momentsToSync } = getState().offline;

    // Check if we're authenticated
    if (!Meteor.userId()) {
      console.log('Not authenticated, cannot sync moments. Current user:', Meteor.userId());

      // Try to login with the resume token
      if (resumeToken) {
        try {
          console.log('Attempting to login with resume token...');
          await new Promise((resolve, reject) => {
            Meteor.loginWithToken(resumeToken, (err) => {
              if (err) {
                console.error('Error logging in with token:', err);
                reject(err);
              } else {
                console.log('Successfully logged in with token');
                resolve();
              }
            });
          });

          // Wait a bit to ensure authentication is complete
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Check if login was successful
          if (!Meteor.userId()) {
            console.log('Login attempt failed, still not authenticated');
            Alert.alert("Cannot Sync", "Authentication failed. Please go online to reset your authentication token.");
            dispatch({ type: OFFLINE_END_SYNC_MOMENTS, payload: { momentsSyncing: false } });
            return;
          }
        } catch (error) {
          console.error('Failed to login with token:', error);
          Alert.alert("Cannot Sync", "Authentication failed. Please go online to reset your authentication token.");
          dispatch({ type: OFFLINE_END_SYNC_MOMENTS, payload: { momentsSyncing: false } });
          return;
        }
      } else {
        Alert.alert("Cannot Sync", "No authentication token found. Please go online to reset your authentication token.");
        dispatch({ type: OFFLINE_END_SYNC_MOMENTS, payload: { momentsSyncing: false } });
        return;
      }
    }

    console.log('Current user ID for sync:', Meteor.userId());

    // Get stored moments from AsyncStorage
    const [storedCheckins, storedCheckouts, storedMoments] = await Promise.all([
      getStoredMoments('offline_checkins'),
      getStoredMoments('offline_checkouts'),
      getStoredMoments('offline_moments')
    ]);

    console.log(`Found ${storedCheckins.length} stored check-ins, ${storedCheckouts.length} stored checkouts, ${storedMoments.length} stored moments in AsyncStorage`);

    // Combine stored moments with Redux moments
    let allMomentsToSync = [...momentsToSync];

    // Add stored moments to the Redux moments to sync if any exist
    if (storedMoments.length > 0) {
      // Add each stored moment if it's not already in the Redux store
      storedMoments.forEach(storedMoment => {
        const isDuplicate = allMomentsToSync.some(
          m => (m.createdAt === storedMoment.createdAt &&
               m.offlineType === storedMoment.offlineType) ||
               (m.momentFieldValues?.originalCreatedAt === storedMoment.momentFieldValues?.originalCreatedAt &&
                m.offlineType === storedMoment.offlineType) ||
               (storedMoment.momentFieldValues?.originalCreatedAt === m.createdAt &&
                m.offlineType === storedMoment.offlineType)
        );

        if (!isDuplicate) {
          allMomentsToSync.push(storedMoment);
        } else {
          // If it's a duplicate, check if the stored moment has more data (like sleep checks)
          // and replace the Redux moment with the stored one if it has more complete data
          const reduxMomentIndex = allMomentsToSync.findIndex(
            m => (m.createdAt === storedMoment.createdAt &&
                 m.offlineType === storedMoment.offlineType) ||
                 (m.momentFieldValues?.originalCreatedAt === storedMoment.momentFieldValues?.originalCreatedAt &&
                  m.offlineType === storedMoment.offlineType) ||
                 (storedMoment.momentFieldValues?.originalCreatedAt === m.createdAt &&
                  m.offlineType === storedMoment.offlineType)
          );

          if (reduxMomentIndex !== -1) {
            const reduxMoment = allMomentsToSync[reduxMomentIndex];
            // If stored moment has sleep checks or end time, prefer it over Redux moment
            if (storedMoment.momentFieldValues?.sleepChecks || storedMoment.momentFieldValues?.endTime) {
              console.log('Replacing Redux moment with more complete stored moment');
              allMomentsToSync[reduxMomentIndex] = storedMoment;
            }
          }
        }
      });

      // Update the Redux store with combined moments
      dispatch({ type: OFFLINE_NEW_MOMENT, payload: { newMoments: allMomentsToSync } });
    }

    // Separate all moments by type (using the combined list)
    const reduxCheckinMoments = allMomentsToSync.filter(moment => moment.offlineType === 'checkIn');
    const reduxCheckoutMoments = allMomentsToSync.filter(moment => moment.offlineType === 'checkOut');
    const nameToFaceMoments = allMomentsToSync.filter(moment => moment.offlineType === 'nameToFace');
    const otherMoments = allMomentsToSync.filter(moment =>
      !['checkIn', 'checkOut', 'nameToFace'].includes(moment.offlineType));

    // Combine and normalize all check-ins
    const allCheckinMoments = [
      ...reduxCheckinMoments.map(normalizeReduxMoment),
      ...storedCheckins.map(normalizeAsyncStorageCheckin)
    ];

    // Combine and normalize all check-outs
    const allCheckoutMoments = [
      ...reduxCheckoutMoments.map(normalizeReduxMoment),
      ...storedCheckouts.map(normalizeAsyncStorageCheckout)
    ];

    // Combine all check-in and check-out moments for chronological processing
    const allCheckInOutMoments = [...allCheckinMoments, ...allCheckoutMoments];

    // Sort all check-in/check-out moments by timestamp to ensure correct order
    allCheckInOutMoments.sort((a, b) => a.createdAt - b.createdAt);

    console.log(`Processing ${allCheckinMoments.length} check-ins, ${allCheckoutMoments.length} check-outs (${allCheckInOutMoments.length} total check-in/out moments), ${nameToFaceMoments.length} name-to-face moments, and ${otherMoments.length} other moments`);

    // Check if we have any moments to sync
    if (allCheckInOutMoments.length === 0 && nameToFaceMoments.length === 0 && otherMoments.length === 0) {
      console.log('No moments to sync');
      dispatch({ type: OFFLINE_END_SYNC_MOMENTS, payload: { momentsSyncing: false } });
      return;
    }

    // Process all check-in/check-out moments sequentially in chronological order
    const checkInOutResult = await processCheckInOutMomentsSequentially(allCheckInOutMoments);

    // Clear AsyncStorage if sync was successful
    if (checkInOutResult.errors === 0) {
      if (storedCheckins.length > 0) {
        try {
          await AsyncStorage.setItem('offline_checkins', JSON.stringify([]));
          console.log('Cleared stored check-ins from AsyncStorage');
        } catch (error) {
          console.error('Error clearing stored check-ins:', error);
        }
      }

      if (storedCheckouts.length > 0) {
        try {
          await AsyncStorage.setItem('offline_checkouts', JSON.stringify([]));
          console.log('Cleared stored checkouts from AsyncStorage');
        } catch (error) {
          console.error('Error clearing stored checkouts:', error);
        }
      }
    }

    // Process name to face moments
    let nameToFaceErrors = 0;
    if (nameToFaceMoments.length > 0) {
      console.log(`Processing ${nameToFaceMoments.length} name to face moments`);
      const nameToFacePromises = nameToFaceMoments.map(moment => {
        return new Promise((resolve) => {
          console.log('Syncing name to face moment:', JSON.stringify({
            offlineType: moment.offlineType,
            taggedPeople: moment.taggedPeople,
            nameToFaceGroupId: moment.nameToFaceGroupId,
            createdAt: moment.createdAt,
            date: moment.date,
            time: moment.time
          }));

          // Use nameToFaceCheckConfirmation method for name to face moments
          // Get the stored data from the moment
          let nameToFaceParams = {};

          // If we have offlineData, use it directly
          if (moment.offlineData) {
            nameToFaceParams = { ...moment.offlineData };

            // Ensure selectedPeople is an array of strings (person IDs)
            if (nameToFaceParams.selectedPeople && Array.isArray(nameToFaceParams.selectedPeople)) {
              // Make sure we're using the correct format for the server method
              console.log('Original selectedPeople:', JSON.stringify(nameToFaceParams.selectedPeople, null, 2));

              // If the first item is an object with tagId, map to tagId
              if (nameToFaceParams.selectedPeople.length > 0 &&
                  typeof nameToFaceParams.selectedPeople[0] === 'object' &&
                  nameToFaceParams.selectedPeople[0].tagId) {
                nameToFaceParams.selectedPeople = nameToFaceParams.selectedPeople.map(p => p.tagId);
                console.log('Converted tagId objects to IDs:', nameToFaceParams.selectedPeople);
              }
              // If the first item is an object with _id, map to _id
              else if (nameToFaceParams.selectedPeople.length > 0 &&
                       typeof nameToFaceParams.selectedPeople[0] === 'object' &&
                       nameToFaceParams.selectedPeople[0]._id) {
                nameToFaceParams.selectedPeople = nameToFaceParams.selectedPeople.map(p => p._id);
                console.log('Converted person objects to IDs:', nameToFaceParams.selectedPeople);
              }
            }

            console.log('Using stored offlineData for nameToFace sync:', nameToFaceParams);
          } else {
            // If we don't have offlineData, we need to recreate it
            // This is a fallback for moments created before the helper function was added
            const { createNameToFaceMoment } = require('../screens/Offline/offlineHelpers');

            // We need to recreate a minimal personInfo and orgInfo from the moment
            const personInfo = {
              _id: moment.owner,
              checkInGroupId: moment.nameToFaceGroupId,
              defaultGroupId: moment.nameToFaceClassroomGroupId
            };

            // Create a temporary moment to extract the offlineData
            const tempMoment = createNameToFaceMoment(
              moment.taggedPeople,
              personInfo,
              moment.currentOrg || {}
            );

            nameToFaceParams = tempMoment.offlineData;
            console.log('Recreated offlineData using helper function:', nameToFaceParams);
          }

          console.log('Calling nameToFaceCheckConfirmation with params:', JSON.stringify(nameToFaceParams, null, 2));

          Meteor.call('nameToFaceCheckConfirmation', nameToFaceParams, (error, result) => {
            if (error) {
              console.log('Error syncing name to face moment: ', error);
              console.log('Error details:', error.details);
              console.log('Error reason:', error.reason);
              nameToFaceErrors++;
              resolve(false);
            } else {
              console.log('Name to face moment synced successfully');
              resolve(true);
            }
          });
        });
      });

      await Promise.all(nameToFacePromises).then((results) => {
        if (nameToFaceErrors > 0) {
          console.log(`${nameToFaceErrors} name to face moments failed to sync`);
        } else {
          console.log('All name to face moments synced successfully');
        }
      });
    }

    // Finally process other moments
    if (otherMoments.length > 0) {
      console.log(`Processing ${otherMoments.length} other moments`);
      const otherMomentsPromises = [];
      otherMoments.forEach((moment, index) => {
        try {
          console.log(`Processing other moment ${index + 1}/${otherMoments.length}:`, {
            momentType: moment.momentType,
            offlineType: moment.offlineType,
            taggedPeople: moment.taggedPeople,
            time: moment.time,
            date: moment.date
          });

          const taggedPeopleCount = Array.isArray(moment.taggedPeople) ? moment.taggedPeople.length : 0;
          console.log(`Syncing ${moment.momentType} moment for ${taggedPeopleCount} people at ${moment.time} on ${moment.date}`);

        // Prepare moment data for server - flatten momentFieldValues
        let momentDataForServer = { ...moment };

    
        // Ensure taggedPeople is properly set for all moments
        if (!momentDataForServer.taggedPeople && momentDataForServer.personId) {
          momentDataForServer.taggedPeople = [`person|${momentDataForServer.personId}`];
          console.log('Added missing taggedPeople for moment:', momentDataForServer.taggedPeople);
        }

        if (moment.momentFieldValues && momentFieldExtractionConfig[moment.momentType]) {
          const fieldsToExtract = momentFieldExtractionConfig[moment.momentType];
          const extractedFieldsForLog = {};

          fieldsToExtract.forEach(key => {
            if (moment.momentFieldValues[key] !== undefined) {
              momentDataForServer[key] = moment.momentFieldValues[key];
              extractedFieldsForLog[key] = moment.momentFieldValues[key];
            }
          });

          if (Object.keys(extractedFieldsForLog).length > 0) {
            console.log(`Extracted fields for ${moment.momentType} moment:`, extractedFieldsForLog);
          }

          // Special handling for sleep checks to set sleepLastSleepCheck if not already set by direct extraction
          if (moment.momentType === 'sleep' && momentDataForServer.sleepChecks && momentDataForServer.sleepChecks.length > 0 && !momentDataForServer.sleepLastSleepCheck) {
            const lastSleepCheck = momentDataForServer.sleepChecks[momentDataForServer.sleepChecks.length - 1];
            if (lastSleepCheck && lastSleepCheck.createdAt) {
              momentDataForServer.sleepLastSleepCheck = lastSleepCheck.createdAt;
              console.log('Set sleepLastSleepCheck from sleepChecks array:', momentDataForServer.sleepLastSleepCheck);
            }
          }
        }

        otherMomentsPromises.push(
          new Promise((resolve) => {
            console.log(`Calling validateMoment for ${moment}`);
            Meteor.call('validateMoment', momentDataForServer, (error, result) => {
              if (error) {
                console.log('Error validating moment: ', error);
                console.log('Error details:', error.details);
                console.log('Error reason:', error.reason);
                resolve(false);
              } else {
                console.log(`validateMoment successful for ${moment.momentType}, now calling insertMoment...`);
                Meteor.call('insertMoment', momentDataForServer, (error, result) => {
                  if (error) {
                    console.log('Error inserting moment: ', error);
                    console.log('Error details:', error.details);
                    console.log('Error reason:', error.reason);
                    resolve(false);
                  } else {
                    console.log(`${moment.momentType} moment inserted successfully with ID:`, result);
                    resolve(true);
                  }
                });
              }
            });
          })
        );
        } catch (error) {
          console.error(`Error processing other moment ${index + 1}:`, error);
          console.error('Moment data that caused error:', JSON.stringify({momentId: moment._id, momentType: moment.momentType, offlineType: moment.offlineType, taggedPeople: moment.taggedPeople, time: moment.time, date: moment.date }, null, 2));
        }
      });

      await Promise.all(otherMomentsPromises).then((results) => {
        const successCount = results.filter(r => r === true).length;
        const failureCount = results.filter(r => r === false).length;
        console.log(`Other moments sync completed: ${successCount} successful, ${failureCount} failed`);
      });
    }

    // Mark sync as complete
    dispatch({ type: OFFLINE_SYNC_MOMENTS });
    dispatch({ type: OFFLINE_CLEAR_MOMENTS });

    // Clear the AsyncStorage if all moments were synced successfully
    try {
      await AsyncStorage.multiSet([
        ['offline_checkins', JSON.stringify([])],
        ['offline_checkouts', JSON.stringify([])],
        ['offline_moments', JSON.stringify([])]
      ]);
      console.log('Cleared stored moments from AsyncStorage');
    } catch (error) {
      console.error('Error clearing stored moments:', error);
    }
  };
}

// Offline check-in actions
export const offlineCheckIn = (people) => {
  return async (dispatch) => {
    try {
      dispatch({ type: OFFLINE_CHECKIN });

      // Update local cache
      const timestamp = moment().valueOf();
      const updates = people.map(person => ({
        ...person,
        checkedIn: true,
        checkInTime: timestamp,
      }));

      // Save to AsyncStorage for person data updates
      const key = 'offline_checkins';
      const existingData = await AsyncStorage.getItem(key);
      const existingCheckins = existingData ? JSON.parse(existingData) : [];

      await AsyncStorage.setItem(key, JSON.stringify([
        ...existingCheckins,
        ...updates
      ]));

      // Also save to AsyncStorage for check-in moments
      // This is separate from the person data and is used for syncing check-in moments
      const checkInsKey = 'offline_check_ins';
      const existingCheckInsData = await AsyncStorage.getItem(checkInsKey);
      const existingCheckInMoments = existingCheckInsData ? JSON.parse(existingCheckInsData) : [];

      // Create check-in moment records for each person
      const checkInMoments = updates.map(person => ({
        _id: person._id,
        personId: person._id,
        firstName: person.firstName,
        lastName: person.lastName,
        checkInTime: timestamp,
        checkInGroupId: person.checkInGroupId,
        groupName: person.groupName,
        createdAt: timestamp,
      }));

      await AsyncStorage.setItem(checkInsKey, JSON.stringify([
        ...existingCheckInMoments,
        ...checkInMoments
      ]));

      console.log(`Saved ${checkInMoments.length} check-in moments to AsyncStorage`);

      // Update the local Meteor collection for each person
      updates.forEach(person => {
        const p = Meteor.collection('people').findOne(person._id);
        if (p) {
          p.checkedIn = true;
          p.checkInTime = timestamp;
          p.checkInGroupId = person.checkInGroupId;
          p.groupName = person.groupName;
          p.checkedInOutTime = timestamp;
          Meteor.collection('people').saveLocal(p);
          console.log(`Updated local Meteor collection for person: ${person._id}`);
        } else {
          console.warn(`Person not found in local Meteor collection: ${person._id}`);
          // If the person doesn't exist in the collection, insert them
          Meteor.collection('people').insert({
            ...person,
            checkedIn: true,
            checkInTime: timestamp,
            checkedInOutTime: timestamp,
            type: 'person'
          });
          console.log(`Inserted person into local Meteor collection: ${person._id}`);
        }
      });

      dispatch({
        type: OFFLINE_CHECKIN_SUCCESS,
        payload: updates
      });

      // Update the Redux state with the fresh list of people
      dispatch(updateOfflinePeople());

    } catch (error) {
      dispatch({
        type: OFFLINE_CHECKIN_ERROR,
        error: error.message
      });
      throw error;
    }
  };
};

// Offline check-out actions
export const offlineCheckOut = (person, checkOutData) => {
  return async (dispatch) => {
    try {
      // Update local cache - create an updated person object with checked out status
      const update = {
        ...person,
        checkedIn: false,
        checkInGroupId: null,
        groupName: null,
        checkedInOutTime: moment().valueOf()
      };

      // Update the local Meteor collection
      const p = Meteor.collection('people').findOne(person._id || person.tagId);
      if (p) {
        p.checkedIn = false;
        p.checkInGroupId = null;
        p.groupName = null;
        p.checkedInOutTime = moment().valueOf();
        Meteor.collection('people').saveLocal(p);
      }

      // Save to AsyncStorage for syncing later
      const key = 'offline_checkouts';
      const existingData = await AsyncStorage.getItem(key);
      const existingCheckouts = existingData ? JSON.parse(existingData) : [];

      // Add the checkout data with timestamp
      const checkoutRecord = {
        ...update,
        checkOutTime: moment().valueOf(),
        checkOutData: checkOutData
      };

      await AsyncStorage.setItem(key, JSON.stringify([
        ...existingCheckouts,
        checkoutRecord
      ]));

      console.log('Updated cached person data for checkout and saved to AsyncStorage:', checkoutRecord);

      // Update the Redux state with the fresh list of people
      dispatch(updateOfflinePeople());

      return true;
    } catch (error) {
      console.error('Error in offlineCheckOut:', error);
      return false;
    }
  };
};

// Action to refresh the offlinePeople state from the Meteor collection
export const updateOfflinePeople = () => {
  return (dispatch) => {
    dispatch({ type: OFFLINE_UPDATE_PEOPLE });
  };
};
