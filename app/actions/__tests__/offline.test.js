import Meteor from 'react-native-meteor';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

// Import the function we want to test
import { syncMoments, addSleepMoments, sleepCheck, endSleep } from '../offline';

// Mock dependencies
jest.mock('react-native-meteor', () => ({
  call: jest.fn(),
  userId: jest.fn(),
  loginWithToken: jest.fn(),
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  multiSet: jest.fn(),
}));

jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn(),
  },
}));

// Mock moment-timezone
jest.mock('moment-timezone', () => {
  const moment = jest.requireActual('moment');
  return moment;
});

// Mock Random utility
jest.mock('../../shared/Random', () => ({
  id: jest.fn(() => 'mock-random-id')
}));

// Mock lodash
jest.mock('lodash', () => ({
  find: jest.fn(),
  filter: jest.fn()
}));

// Mock console.log to reduce noise in tests
const originalConsoleLog = console.log;
beforeAll(() => {
  console.log = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
});

// Helper function to create mock moments
const createMockMoment = (offlineType, personId, createdAt, additionalData = {}) => {
  const baseData = {
    personId,
    offlineType,
    momentType: offlineType,
    createdAt,
    prettyTime: new Date(createdAt).toLocaleTimeString(),
    orgId: 'org123',
    originalMoment: {
      personId,
      offlineType,
      momentType: offlineType,
      createdAt,
      currentOrg: { _id: 'org123' },
      ...additionalData
    }
  };

  if (offlineType === 'checkIn') {
    return {
      ...baseData,
      comment: additionalData.comment || 'Check-in comment',
      groupId: additionalData.groupId || 'group123',
      groupName: additionalData.groupName || 'Test Group',
      checkedInById: additionalData.checkedInById || 'user123',
    };
  } else if (offlineType === 'checkOut') {
    return {
      ...baseData,
      comment: additionalData.comment || 'Check-out comment',
      mood: additionalData.mood || 'happy',
      checkedOutById: additionalData.checkedOutById || 'user123',
    };
  }

  return baseData;
};

describe('processCheckInOutMomentsSequentially Tests', () => {
  let mockDispatch;
  let mockGetState;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockDispatch = jest.fn();
    mockGetState = jest.fn();

    // Mock authenticated user
    Meteor.userId.mockReturnValue('user123');

    // Mock empty AsyncStorage by default
    AsyncStorage.getItem.mockResolvedValue(JSON.stringify([]));
    AsyncStorage.setItem.mockResolvedValue();
    AsyncStorage.multiSet.mockResolvedValue();
  });

  describe('Basic Functionality', () => {
    it('should process empty array without errors', async () => {
      mockGetState.mockReturnValue({
        auth: { resumeToken: 'token123' },
        offline: { momentsToSync: [] }
      });

      const syncAction = syncMoments();
      await syncAction(mockDispatch, mockGetState);

      // Should not call Meteor.call for empty array
      expect(Meteor.call).not.toHaveBeenCalled();
      
      // Should still dispatch end sync action
      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'OFFLINE_END_SYNC_MOMENTS',
        payload: { momentsSyncing: false }
      });
    });

    it('should process single check-in moment successfully', async () => {
      const baseTime = Date.now();
      const personId = 'person123';

      const moments = [
        createMockMoment('checkIn', personId, baseTime)
      ];

      Meteor.call.mockImplementation((method, data, callback) => {
        if (method === 'checkIn') {
          expect(data.personId).toBe(personId);
          expect(data.createdAt).toBe(baseTime);
          expect(data.comments).toBe('Check-in comment'); // Server expects 'comments'
          callback(null, { success: true });
        } else if (method === 'resolveRaceConditionsForPerson') {
          expect(data.personId).toBe(personId);
          callback(null, { resolved: true }); // Mock successful resolution
        } else {
          // Optional: Fail test for unexpected methods, or log them
          console.error('Unexpected Meteor method called in test:', method);
          callback(new Error(`Unexpected Meteor method: ${method}`));
        }
      });

      mockGetState.mockReturnValue({
        auth: { resumeToken: 'token123' },
        offline: { momentsToSync: moments }
      });

      const syncAction = syncMoments();
      await syncAction(mockDispatch, mockGetState);

      expect(Meteor.call).toHaveBeenCalledTimes(2); // Increased from 1 to 2
    });

    it('should process single check-out moment successfully', async () => {
      const baseTime = Date.now();
      const personId = 'person123';

      const moments = [
        createMockMoment('checkOut', personId, baseTime, { mood: 'excited' })
      ];

      Meteor.call.mockImplementation((method, data, callback) => {
        if (method === 'checkOut') {
          expect(data.personId).toBe(personId);
          expect(data.createdAt).toBe(baseTime);
          expect(data.comments).toBe('Check-out comment'); // Server expects 'comments'
          expect(data.mood).toBe('excited');
          callback(null, { success: true });
        } else if (method === 'resolveRaceConditionsForPerson') {
          expect(data.personId).toBe(personId);
          callback(null, { resolved: true });
        } else {
          console.error('Unexpected Meteor method called in test:', method);
          callback(new Error(`Unexpected Meteor method: ${method}`));
        }
      });

      mockGetState.mockReturnValue({
        auth: { resumeToken: 'token123' },
        offline: { momentsToSync: moments }
      });

      const syncAction = syncMoments();
      await syncAction(mockDispatch, mockGetState);

      expect(Meteor.call).toHaveBeenCalledTimes(2); // Increased from 1 to 2
    });
  });

  describe('Chronological Processing', () => {
    it('should process check-in and check-out moments in chronological order', async () => {
      const baseTime = Date.now();
      const personId = 'person123';
      
      const moments = [
        createMockMoment('checkIn', personId, baseTime),
        createMockMoment('checkOut', personId, baseTime + 1000),
        createMockMoment('checkIn', personId, baseTime + 2000),
        createMockMoment('checkOut', personId, baseTime + 3000),
      ];

      const meteorCallOrder = [];
      Meteor.call.mockImplementation((method, data, callback) => {
        if (method === 'checkIn' || method === 'checkOut') {
          meteorCallOrder.push({ method, personId: data.personId, timestamp: data.createdAt });
          callback(null, { success: true });
        } else if (method === 'resolveRaceConditionsForPerson') {
          // For this test, we expect one call for the unique personId
          expect(data.personId).toBe(personId);
          meteorCallOrder.push({ method, personId: data.personId }); // Log this call too if needed for order verification
          callback(null, { resolved: true });
        } else {
          console.error('Unexpected Meteor method called in test:', method);
          callback(new Error(`Unexpected Meteor method: ${method}`));
        }
      });

      mockGetState.mockReturnValue({
        auth: { resumeToken: 'token123' },
        offline: { momentsToSync: moments }
      });

      const syncAction = syncMoments();
      await syncAction(mockDispatch, mockGetState);

      // Verify that Meteor.call was called 5 times (4 moments + 1 race condition for the unique person)
      expect(Meteor.call).toHaveBeenCalledTimes(5); // Increased from 4 to 5
      
      // Verify the order of primary calls matches chronological order
      const primaryCalls = meteorCallOrder.filter(call => call.method === 'checkIn' || call.method === 'checkOut');
      expect(primaryCalls).toEqual([
        { method: 'checkIn', personId, timestamp: baseTime },
        { method: 'checkOut', personId, timestamp: baseTime + 1000 },
        { method: 'checkIn', personId, timestamp: baseTime + 2000 },
        { method: 'checkOut', personId, timestamp: baseTime + 3000 },
      ]);
      // Also verify the race condition call happened for the correct personId
      const raceConditionCall = meteorCallOrder.find(call => call.method === 'resolveRaceConditionsForPerson');
      expect(raceConditionCall).toBeDefined();
      expect(raceConditionCall.personId).toBe(personId);
    });
  });

  describe('Error Handling', () => {
    it('should handle "Person already checked in" error as success', async () => {
      const baseTime = Date.now();
      const personId = 'person123';
      
      const moments = [
        createMockMoment('checkIn', personId, baseTime),
      ];

      Meteor.call.mockImplementation((method, data, callback) => {
        if (method === 'checkIn') {
          const error = new Error('Person already checked in');
          error.reason = 'Person already checked in';
          callback(error, null);
        } else if (method === 'resolveRaceConditionsForPerson') {
          // This should still be called as the check-in error is treated as success
          expect(data.personId).toBe(personId);
          callback(null, { resolved: true });
        } else {
          console.error('Unexpected Meteor method called in test:', method);
          callback(new Error(`Unexpected Meteor method: ${method}`));
        }
      });

      mockGetState.mockReturnValue({
        auth: { resumeToken: 'token123' },
        offline: { momentsToSync: moments }
      });

      const syncAction = syncMoments();
      await syncAction(mockDispatch, mockGetState);

      // Verify the call was made (checkIn attempt + race condition resolution)
      expect(Meteor.call).toHaveBeenCalledTimes(2); // Increased from 1 to 2
    });

    it('should handle server errors gracefully', async () => {
      const baseTime = Date.now();
      const personId = 'person123';
      
      const moments = [
        createMockMoment('checkIn', personId, baseTime),
      ];

      // Mock the call to return a server error
      Meteor.call.mockImplementation((method, data, callback) => {
        callback(new Error('Server error'), null);
      });

      mockGetState.mockReturnValue({
        auth: { resumeToken: 'token123' },
        offline: { momentsToSync: moments }
      });

      const syncAction = syncMoments();
      await syncAction(mockDispatch, mockGetState);

      // Verify the call was made despite the error
      // In this case, if checkIn fails with a real error, resolveRaceConditionsForPerson is NOT called.
      expect(Meteor.call).toHaveBeenCalledTimes(1);
    });
  });

  describe('Data Validation', () => {
    it('should transform comment field to comments for server', async () => {
      const baseTime = Date.now();
      const personId = 'person123';
      
      const moments = [
        createMockMoment('checkIn', personId, baseTime, { comment: 'Test check-in comment' }),
      ];

      Meteor.call.mockImplementation((method, data, callback) => {
        if (method === 'checkIn') {
          expect(data.comments).toBe('Test check-in comment');
          expect(data.comment).toBe('Test check-in comment');
          callback(null, { success: true });
        } else if (method === 'resolveRaceConditionsForPerson') {
          expect(data.personId).toBe(personId);
          callback(null, { resolved: true });
        } else {
          console.error('Unexpected Meteor method called in test:', method);
          callback(new Error(`Unexpected Meteor method: ${method}`));
        }
      });

      mockGetState.mockReturnValue({
        auth: { resumeToken: 'token123' },
        offline: { momentsToSync: moments }
      });

      const syncAction = syncMoments();
      await syncAction(mockDispatch, mockGetState);

      expect(Meteor.call).toHaveBeenCalledTimes(2); // Increased from 1 to 2
    });

    it('should include all required fields for check-in', async () => {
      const baseTime = Date.now();
      const personId = 'person123';
      
      const moments = [
        createMockMoment('checkIn', personId, baseTime, {
          groupId: 'group456',
          groupName: 'Test Group',
          checkedInById: 'user789'
        })
      ];

      Meteor.call.mockImplementation((method, data, callback) => {
        if (method === 'checkIn') {
          expect(data.personId).toBe(personId);
          expect(data.offlineType).toBe('checkIn');
          expect(data.momentType).toBe('checkIn');
          expect(data.createdAt).toBe(baseTime);
          expect(data.prettyTime).toBeDefined();
          expect(data.groupId).toBe('group456');
          expect(data.groupName).toBe('Test Group');
          expect(data.checkedInById).toBe('user789');
          callback(null, { success: true });
        } else if (method === 'resolveRaceConditionsForPerson') {
          expect(data.personId).toBe(personId);
          callback(null, { resolved: true });
        } else {
          console.error('Unexpected Meteor method called in test:', method);
          callback(new Error(`Unexpected Meteor method: ${method}`));
        }
      });

      mockGetState.mockReturnValue({
        auth: { resumeToken: 'token123' },
        offline: { momentsToSync: moments }
      });

      const syncAction = syncMoments();
      await syncAction(mockDispatch, mockGetState);

      expect(Meteor.call).toHaveBeenCalledTimes(2); // Increased from 1 to 2
    });
  });
});

describe('Sleep Methods Tests', () => {
  let mockDispatch;
  let mockGetState;
  const _ = require('lodash');

  beforeEach(() => {
    jest.clearAllMocks();

    mockDispatch = jest.fn();
    mockGetState = jest.fn();

    // Mock AsyncStorage
    AsyncStorage.getItem.mockResolvedValue(JSON.stringify([]));
    AsyncStorage.setItem.mockResolvedValue();
  });

  describe('addSleepMoments', () => {
    it('should create sleep moments for tagged people with string IDs', async () => {
      const taggedPeople = ['person1', 'person2'];
      const mockPeople = [
        { _id: 'person1', firstName: 'John', lastName: 'Doe' },
        { _id: 'person2', firstName: 'Jane', lastName: 'Smith' }
      ];
      const mockCurrentOrg = { _id: 'org123', name: 'Test Org' };

      mockGetState.mockReturnValue({
        auth: {
          offlinePeople: mockPeople,
          currentOrg: mockCurrentOrg
        }
      });

      _.find.mockImplementation((people, criteria) => {
        return people.find(p => p._id === criteria._id);
      });

      const addSleepMomentsAction = addSleepMoments(taggedPeople);
      await addSleepMomentsAction(mockDispatch, mockGetState);

      // Should dispatch saveMoment for each person
      expect(mockDispatch).toHaveBeenCalledTimes(3); // 2 saveMoment calls + 1 OFFLINE_ADD_SLEEP_MOMENTS

      // Check that OFFLINE_ADD_SLEEP_MOMENTS was dispatched with correct payload
      const addSleepMomentsCall = mockDispatch.mock.calls.find(call =>
        call[0].type === 'OFFLINE_ADD_SLEEP_MOMENTS'
      );
      expect(addSleepMomentsCall).toBeDefined();
      expect(addSleepMomentsCall[0].payload.newMoments).toHaveLength(2);
      expect(addSleepMomentsCall[0].payload.newMoments[0]).toMatchObject({
        _id: 'mock-random-id',
        firstName: 'John',
        lastName: 'Doe',
        personId: 'person1',
        offlineType: 'sleep',
        momentType: 'sleep'
      });
    });

    it('should handle tagged people with object format', async () => {
      const taggedPeople = [
        { tagId: 'person1', tagLabel: 'John Doe' },
        { tagId: 'person2', tagLabel: 'Jane Smith' }
      ];
      const mockPeople = [
        { _id: 'person1', firstName: 'John', lastName: 'Doe' },
        { _id: 'person2', firstName: 'Jane', lastName: 'Smith' }
      ];
      const mockCurrentOrg = { _id: 'org123', name: 'Test Org' };

      mockGetState.mockReturnValue({
        auth: {
          offlinePeople: mockPeople,
          currentOrg: mockCurrentOrg
        }
      });

      _.find.mockImplementation((people, criteria) => {
        return people.find(p => p._id === criteria._id);
      });

      const addSleepMomentsAction = addSleepMoments(taggedPeople);
      await addSleepMomentsAction(mockDispatch, mockGetState);

      expect(mockDispatch).toHaveBeenCalledTimes(3); // 2 saveMoment calls + 1 OFFLINE_ADD_SLEEP_MOMENTS
    });

    it('should handle empty tagged people array', async () => {
      const taggedPeople = [];

      mockGetState.mockReturnValue({
        auth: {
          offlinePeople: [],
          currentOrg: { _id: 'org123' }
        }
      });

      const addSleepMomentsAction = addSleepMoments(taggedPeople);
      await addSleepMomentsAction(mockDispatch, mockGetState);

      // Should only dispatch OFFLINE_ADD_SLEEP_MOMENTS with empty array
      expect(mockDispatch).toHaveBeenCalledTimes(1);
      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'OFFLINE_ADD_SLEEP_MOMENTS',
        payload: { newMoments: [] }
      });
    });

    it('should skip people not found in offlinePeople', async () => {
      const taggedPeople = ['person1', 'person999']; // person999 doesn't exist
      const mockPeople = [
        { _id: 'person1', firstName: 'John', lastName: 'Doe' }
      ];

      mockGetState.mockReturnValue({
        auth: {
          offlinePeople: mockPeople,
          currentOrg: { _id: 'org123' }
        }
      });

      _.find.mockImplementation((people, criteria) => {
        return people.find(p => p._id === criteria._id);
      });

      const addSleepMomentsAction = addSleepMoments(taggedPeople);
      await addSleepMomentsAction(mockDispatch, mockGetState);

      // Should only create moment for person1
      expect(mockDispatch).toHaveBeenCalledTimes(2); // 1 saveMoment call + 1 OFFLINE_ADD_SLEEP_MOMENTS
      const addSleepMomentsCall = mockDispatch.mock.calls.find(call =>
        call[0].type === 'OFFLINE_ADD_SLEEP_MOMENTS'
      );
      expect(addSleepMomentsCall[0].payload.newMoments).toHaveLength(1);
    });
  });

  describe('sleepCheck', () => {
    it('should add sleep check to existing sleep moment', async () => {
      const sleepMomentId = 'sleep-moment-1';
      const createdAt = Date.now() - 10000;
      const mockSleepMoment = {
        _id: sleepMomentId,
        personId: 'person1',
        createdAt: createdAt,
        sleepChecks: [],
        momentFieldValues: {
          sleepDidNotSleep: false,
          sleepSatQuietly: false
        }
      };
      const mockPersonInfo = { _id: 'user123' };

      mockGetState.mockReturnValue({
        offline: {
          sleepMoments: [mockSleepMoment]
        },
        auth: {
          personInfo: mockPersonInfo
        }
      });

      _.find.mockReturnValue(mockSleepMoment);

      // Mock AsyncStorage to return a moment that can be found and updated
      AsyncStorage.getItem.mockResolvedValue(JSON.stringify([
        {
          personId: 'person1',
          createdAt: createdAt,
          momentFieldValues: {
            sleepDidNotSleep: false,
            sleepSatQuietly: false
          }
        }
      ]));

      const sleepCheckAction = sleepCheck(sleepMomentId, {
        sleepPosition: 'Back',
        distressedSleep: true
      });
      await sleepCheckAction(mockDispatch, mockGetState);

      // Should dispatch OFFLINE_SLEEP_CHECK
      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'OFFLINE_SLEEP_CHECK',
        payload: { sleepMoment: expect.objectContaining({
          _id: sleepMomentId,
          sleepChecks: expect.arrayContaining([
            expect.objectContaining({
              personId: 'person1',
              sleepPosition: 'Back',
              distressedSleep: true,
              createdBy: 'user123'
            })
          ])
        })}
      });

      // Should update AsyncStorage
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should handle sleep check without options', async () => {
      const sleepMomentId = 'sleep-moment-1';
      const mockSleepMoment = {
        _id: sleepMomentId,
        personId: 'person1',
        createdAt: Date.now() - 10000,
        sleepChecks: [],
        momentFieldValues: {}
      };

      mockGetState.mockReturnValue({
        offline: {
          sleepMoments: [mockSleepMoment]
        },
        auth: {
          personInfo: { _id: 'user123' }
        }
      });

      _.find.mockReturnValue(mockSleepMoment);

      const sleepCheckAction = sleepCheck(sleepMomentId);
      await sleepCheckAction(mockDispatch, mockGetState);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'OFFLINE_SLEEP_CHECK',
        payload: { sleepMoment: expect.objectContaining({
          _id: sleepMomentId,
          sleepChecks: expect.arrayContaining([
            expect.objectContaining({
              personId: 'person1',
              createdBy: 'user123'
            })
          ])
        })}
      });
    });

    it('should return early if sleep moment not found', async () => {
      const sleepMomentId = 'non-existent-moment';

      mockGetState.mockReturnValue({
        offline: {
          sleepMoments: []
        },
        auth: {
          personInfo: { _id: 'user123' }
        }
      });

      _.find.mockReturnValue(undefined);

      const sleepCheckAction = sleepCheck(sleepMomentId);
      await sleepCheckAction(mockDispatch, mockGetState);

      // Should not dispatch anything
      expect(mockDispatch).not.toHaveBeenCalled();
      expect(AsyncStorage.setItem).not.toHaveBeenCalled();
    });

    it('should preserve existing sleep checks', async () => {
      const sleepMomentId = 'sleep-moment-1';
      const existingSleepCheck = {
        personId: 'person1',
        createdAt: Date.now() - 5000,
        sleepPosition: 'Left Side',
        createdBy: 'user123'
      };
      const mockSleepMoment = {
        _id: sleepMomentId,
        personId: 'person1',
        createdAt: Date.now() - 10000,
        sleepChecks: [existingSleepCheck],
        momentFieldValues: {}
      };

      mockGetState.mockReturnValue({
        offline: {
          sleepMoments: [mockSleepMoment]
        },
        auth: {
          personInfo: { _id: 'user123' }
        }
      });

      _.find.mockReturnValue(mockSleepMoment);

      const sleepCheckAction = sleepCheck(sleepMomentId, { sleepPosition: 'Right Side' });
      await sleepCheckAction(mockDispatch, mockGetState);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'OFFLINE_SLEEP_CHECK',
        payload: { sleepMoment: expect.objectContaining({
          sleepChecks: expect.arrayContaining([
            existingSleepCheck,
            expect.objectContaining({
              sleepPosition: 'Right Side'
            })
          ])
        })}
      });
    });
  });

  describe('endSleep', () => {
    it('should end sleep moment and update AsyncStorage', async () => {
      const sleepMomentId = 'sleep-moment-1';
      const mockSleepMoment = {
        _id: sleepMomentId,
        personId: 'person1',
        createdAt: Date.now() - 10000,
        time: '2:00 PM',
        sleepChecks: [
          {
            personId: 'person1',
            createdAt: Date.now() - 5000,
            sleepPosition: 'Back'
          }
        ],
        momentFieldValues: {
          sleepDidNotSleep: false,
          sleepSatQuietly: false
        }
      };

      mockGetState.mockReturnValue({
        offline: {
          sleepMoments: [mockSleepMoment]
        },
        auth: {
          personInfo: { _id: 'user123' },
          currentOrg: { _id: 'org123' }
        }
      });

      _.find.mockReturnValue(mockSleepMoment);

      // Mock AsyncStorage to return existing moment
      AsyncStorage.getItem.mockResolvedValue(JSON.stringify([
        {
          createdAt: mockSleepMoment.createdAt,
          momentFieldValues: {
            sleepChecks: mockSleepMoment.sleepChecks,
            sleepDidNotSleep: false
          }
        }
      ]));

      const endSleepAction = endSleep(sleepMomentId, {
        endTime: '3:00 PM',
        sleepDidNotSleep: false,
        sleepSatQuietly: true,
        comment: 'Slept well'
      });
      await endSleepAction(mockDispatch, mockGetState);

      // Should dispatch OFFLINE_END_SLEEP
      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'OFFLINE_END_SLEEP',
        payload: { sleepMoment: expect.objectContaining({
          _id: sleepMomentId,
          endTime: '3:00 PM',
          sleepDidNotSleep: false,
          sleepSatQuietly: true,
          comment: 'Slept well'
        })}
      });

      // Should update AsyncStorage
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should preserve sleep checks from AsyncStorage', async () => {
      const sleepMomentId = 'sleep-moment-1';
      const mockSleepMoment = {
        _id: sleepMomentId,
        personId: 'person1',
        createdAt: Date.now() - 10000,
        sleepChecks: [],
        momentFieldValues: {}
      };

      const storedSleepChecks = [
        {
          personId: 'person1',
          createdAt: Date.now() - 5000,
          sleepPosition: 'Back'
        },
        {
          personId: 'person1',
          createdAt: Date.now() - 3000,
          sleepPosition: 'Left Side'
        }
      ];

      mockGetState.mockReturnValue({
        offline: {
          sleepMoments: [mockSleepMoment]
        },
        auth: {
          personInfo: { _id: 'user123' },
          currentOrg: { _id: 'org123' }
        }
      });

      _.find.mockReturnValue(mockSleepMoment);

      // Mock AsyncStorage to return moment with sleep checks
      AsyncStorage.getItem.mockResolvedValue(JSON.stringify([
        {
          createdAt: mockSleepMoment.createdAt,
          momentFieldValues: {
            sleepChecks: storedSleepChecks
          }
        }
      ]));

      const endSleepAction = endSleep(sleepMomentId, {
        endTime: '3:00 PM'
      });
      await endSleepAction(mockDispatch, mockGetState);

      // Verify that AsyncStorage.setItem was called with preserved sleep checks
      const setItemCalls = AsyncStorage.setItem.mock.calls;
      const updateCall = setItemCalls.find(call => call[0] === 'offline_moments');
      expect(updateCall).toBeDefined();

      const updatedMoments = JSON.parse(updateCall[1]);
      const updatedMoment = updatedMoments[0];
      expect(updatedMoment.momentFieldValues.sleepChecks).toEqual(storedSleepChecks);
    });

    it('should return early if sleep moment not found', async () => {
      const sleepMomentId = 'non-existent-moment';

      mockGetState.mockReturnValue({
        offline: {
          sleepMoments: []
        },
        auth: {
          personInfo: { _id: 'user123' },
          currentOrg: { _id: 'org123' }
        }
      });

      _.find.mockReturnValue(undefined);

      const endSleepAction = endSleep(sleepMomentId);
      await endSleepAction(mockDispatch, mockGetState);

      // Should not dispatch anything
      expect(mockDispatch).not.toHaveBeenCalled();
    });

    it('should handle missing AsyncStorage data gracefully', async () => {
      const sleepMomentId = 'sleep-moment-1';
      const mockSleepMoment = {
        _id: sleepMomentId,
        personId: 'person1',
        createdAt: Date.now() - 10000,
        sleepChecks: [{ personId: 'person1', createdAt: Date.now() - 5000 }],
        momentFieldValues: {}
      };

      mockGetState.mockReturnValue({
        offline: {
          sleepMoments: [mockSleepMoment]
        },
        auth: {
          personInfo: { _id: 'user123' },
          currentOrg: { _id: 'org123' }
        }
      });

      _.find.mockReturnValue(mockSleepMoment);

      // Mock AsyncStorage to return empty array
      AsyncStorage.getItem.mockResolvedValue(JSON.stringify([]));

      const endSleepAction = endSleep(sleepMomentId, {
        endTime: '3:00 PM'
      });
      await endSleepAction(mockDispatch, mockGetState);

      // Should still dispatch and use sleep checks from Redux state
      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'OFFLINE_END_SLEEP',
        payload: { sleepMoment: expect.objectContaining({
          _id: sleepMomentId,
          endTime: '3:00 PM'
        })}
      });
    });
  });
});
