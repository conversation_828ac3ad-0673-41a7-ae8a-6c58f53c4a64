import Signin from '../components/SignIn';
import React from 'react';
import {render, screen, fireEvent, userEvent, waitFor} from '../testUtils';
import {onSignIn} from '../components/utils/signin';
import {Alert} from 'react-native';
import Meteor from 'react-native-meteor';

jest.spyOn(Alert, 'alert');
jest.mock('expo-updates');

describe('Signin Renders', () => {
  beforeEach(() => {
    render(<Signin />);
  });
  it('should render', async () => {
    expect(screen.getByTestId('signin-submit')).toBeTruthy();
  });
  it('should not show custom server', async () => {
    expect(screen.queryByTestId('signin-customUrl')).toBeNull();
  });
  it('should input username/email', async () => {
    const username = screen.getByTestId('signin-email');
    fireEvent.changeText(username, 'user');
    expect(username.props.value).toBe('user');
  });
  it('should input password', async () => {
    const password = screen.getByTestId('signin-password');
    fireEvent.changeText(password, 'pass');
    expect(password.props.value).toBe('pass');
  });
  it('should submit', async () => {
    const username = screen.getByTestId('signin-email');
    const password = screen.getByTestId('signin-password');
    const submit = screen.getByTestId('signin-submit');
    const user = userEvent.setup();
    fireEvent.changeText(username, 'user');
    fireEvent.changeText(password, 'pass');
    await user.press(submit);
    const spinner = screen.getByTestId('signin-spinner');
    expect(spinner).toBeTruthy();
  });
});
describe('Cognito reset password', () => {
  beforeEach(() => {
    render(<Signin />);
  });
  it('should show code confirmation', async () => {
    Meteor.call.mockImplementationOnce((method, email, callback) => {
      callback(null, true);
    });
    const username = screen.getByTestId('signin-email');
    const user = userEvent.setup();
    await user.type(username, '<EMAIL>');
    expect(username.props.value).toBe('<EMAIL>');
    const passwordHelp = screen.getByText('Password help');
    await user.press(passwordHelp);

    expect(Alert.alert).toHaveBeenCalledWith(
      'Please check your email for a verification code',
    );
  });
  it('should show missing email error', async () => {
    const passwordHelp = screen.getByText('Password help');
    const user = userEvent.setup();
    await user.press(passwordHelp);
    await expect(Alert.alert).toHaveBeenCalledWith(
      'Please enter your username first.',
    );
  });
});
describe('Sign in logic (Legacy)', () => {
  beforeEach(() => {
    render(<Signin />);
  });
  it('should show email error', async () => {
    const options = {
      setState: jest.fn(),
      saveAndConnect: jest.fn(),
      email: '',
      navigation: undefined,
      org: null,
      personInfo: null,
      pinCodePrompt: undefined,
      route: undefined,
    };
    expect(onSignIn(options)).toBe(
      options.setState({error: 'You must enter an email address'}),
    );
  });
  it('should show password error', async () => {
    const options = {
      setState: jest.fn(),
      saveAndConnect: jest.fn(),
      email: '<EMAIL>',
      navigation: undefined,
      org: null,
      personInfo: null,
      pinCodePrompt: undefined,
      route: undefined,
    };
    expect(onSignIn(options)).toBe(
      options.setState({error: 'You must enter a password'}),
    );
  });
  it('should show password error', async () => {
    const options = {
      setState: jest.fn(),
      saveAndConnect: jest.fn(),
      email: '<EMAIL>',
      password: 'password',
      navigation: jest.fn(),
      org: null,
      personInfo: null,
      pinCodePrompt: undefined,
      route: undefined,
    };
    expect(onSignIn(options)).toBe(
      options.setState({error: 'You must enter a password'}),
    );
  });
  it('should sign in succesfully', async () => {
    const options = {
      setState: jest.fn(),
      saveAndConnect: jest.fn(),
      email: '<EMAIL>',
      password: 'password',
      navigation: jest.fn(),
      org: null,
      personInfo: null,
      pinCodePrompt: undefined,
      route: undefined,
      dispatch: jest.fn(),
    };
    const clearAppBackground = jest.fn();
    expect(onSignIn(options)).toBe(options.dispatch(clearAppBackground()));
  });
});
export const webWait = async expectationFn => {
  let attempts = 0;
  let success = false;
  let maxAttempts = 3;
  let delay = 8000;

  while (attempts < maxAttempts && !success) {
    try {
      await expectationFn();
      success = true;
    } catch (error) {
      attempts++;
      if (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  if (!success) {
    throw new Error(
      `Expectation, ${expectationFn} did not succeed after ${maxAttempts} attempts`,
    );
  }
};
