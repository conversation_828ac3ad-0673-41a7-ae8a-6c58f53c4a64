import React from 'react';
import 'react-native-gesture-handler/jestSetup';
import '@testing-library/react-native/extend-expect';
import mockAsyncStorage from '@react-native-async-storage/async-storage/jest/async-storage-mock';

jest.mock('./app/api/Orgs', () => {
  const mockOrgs = [
    {
      _id: '0000',
      name: 'Org 1',
      address: '1234 Main St',
      city: 'Springfield',
      state: 'IL'
    },
  ];
  return {
    current: jest.fn(() => ({
      _id: 'mockOrgId',
      getAvailableAssessmentLevels: jest.fn(),
      getTimezone: jest.fn().mockReturnValue('America/Chicago'),
    })),
    hasCustomization: jest.fn(() => true),
    find: jest.fn(() => mockOrgs)
  };
});
jest.mock('./app/api/Invoices', () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  insert: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  subscribe: jest.fn(),
}));
jest.mock('./app/api/Relationships', () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  insert: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  subscribe: jest.fn(),
}));
jest.mock('./app/api/Curriculums', () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  insert: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  subscribe: jest.fn(),
  activitiesAgeGroups: jest.fn()
}));
jest.mock('./app/api/Food', () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  insert: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  subscribe: jest.fn(),
  findWithRecurrence: jest.fn(),
}));
jest.mock('./app/api/People', () => ({
  find: jest.fn(),
  findOne: jest.fn((type = 'admin', user) => (user ?? { firstName: 'Test', lastName: 'Person', type })),
  insert: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  subscribe: jest.fn(),
}));
jest.mock('./app/api/Messages', () => ({
  find: jest.fn(),
}));
jest.mock('./app/api/Groups', () => ({
  find: jest.fn(() => [
    { _id: 'group1', name: 'Group 1' },
    { _id: 'group2', name: 'Group 2' }
  ]),
}));
jest.mock('./app/api/Moments', () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  insert: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  subscribe: jest.fn()
}));
jest.mock('react-native-meteor', () => {
  const Meteor = {
    isClient: true,
    isServer: true,
    methods: jest.fn(),
    call: jest.fn(),
    loginWithPassword: jest.fn(),
    logout: jest.fn(),
    subscribe: jest.fn(),
    userId: 'mockUserId',
    user: jest.fn().mockReturnValue({_id: '1234', orgId: '0000', checkInGroupId: 'group1'}),
    reconnect: jest.fn(),
    disconnect: jest.fn(),
    connected: true,
    callPromise: jest.fn(),
    unsubscribe: jest.fn()
  };
  const status = jest.fn().mockReturnValue({connected: true})
  const call = jest.fn()
  const user = jest.fn();
  const subscribe = jest.fn(() => ({
    ready: jest.fn().mockReturnValue(true),
    stop: jest.fn(),
  }))
  const getAllLocalStorageAccounts = jest.fn(() => Promise.resolve([]))
  const withTracker = callback => Component => {
    return props => <Component {...props}/>;
  }
  return {Meteor, withTracker, subscribe, call, user, getAllLocalStorageAccounts, status};
})
jest.mock('@react-native-community/netinfo', () => {
  const RNCNetInfoMock = require('@react-native-community/netinfo/jest/netinfo-mock.js').default;
  return {
    ...RNCNetInfoMock,
    fetch: jest.fn().mockResolvedValue({
      type: 'cellular',
      isConnected: true,
      isInternetReachable: true,
      details: {
        isConnectionExpensive: true,
        cellularGeneration: '3g',
      },
    }),
  };
});
jest.mock('react-native-keyboard-aware-scroll-view', () => {
  const KeyboardAwareScrollView = ({children}) => children;
  return {KeyboardAwareScrollView};
});
jest.mock('react-native-picker', () => {
  return () => ({
    hide: jest.fn(),
    show: jest.fn(),
  });
});
jest.mock('react-native-device-info', () => {
  return {
    __esModule: true,
    default: {
      getVersion: jest.fn(() => '1.0.0'),
      getBuildNumber: jest.fn(() => '1'),
    }

  };
});
jest.mock('./app/components/icons/Nucleo', () => {
  return props => <mockIcon {...props}>icon</mockIcon>;
});
jest.mock('react-native/Libraries/EventEmitter/NativeEventEmitter');
jest.mock('@bugsnag/react-native');
jest.mock('./app/shared/Settings', () => ({
  MOBILE_API_KEY: 'mocked-key',
}));
jest.mock('native-base', (children) => ({children}))
jest.mock('react-native-webview', () => 'Webview');
jest.mock('@react-native-cookies/cookies', () => 'Cookies');
jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);
jest.mock('react-native-vector-icons/Ionicons', () => 'Icon');
jest.mock('react-native-vector-icons/Entypo', () => 'Icon');
jest.mock('react-native-vector-icons/Octicons', () => 'Icon');
jest.mock('react-native-vector-icons/AntDesign', () => 'Icon');
jest.mock('react-native-vector-icons/MaterialIcons', () => 'Icon');
jest.mock('react-native-vector-icons/MaterialCommunityIcons', () => 'Icon');
jest.mock('react-native-vector-icons/Feather', () => 'Icon');
jest.mock('react-native-vector-icons/FontAwesome5', () => 'Icon');
jest.mock('react-native-vector-icons/FontAwesome', () => 'Icon');
jest.mock('react-native-vector-icons/EvilIcons', () => 'Icon');
jest.mock('./app/images/override/login_logo.png');
jest.mock('react-native/Libraries/EventEmitter/NativeEventEmitter');
jest.mock('react-native-webview', () => 'Webview');
jest.mock('@react-native-cookies/cookies', () => 'Cookies');
jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);
jest.mock('react-native-vector-icons/Ionicons', () => 'Icon');
jest.mock('react-native-vector-icons/Entypo', () => 'Icon');
jest.mock('react-native-vector-icons/Octicons', () => 'Icon');
jest.mock('react-native-vector-icons/AntDesign', () => 'Icon');
jest.mock('react-native-vector-icons/MaterialIcons', () => 'Icon');
jest.mock('react-native-vector-icons/MaterialCommunityIcons', () => 'Icon');
jest.mock('react-native-vector-icons/Feather', () => 'Icon');
jest.mock('react-native-vector-icons/FontAwesome5', () => 'Icon');
jest.mock('react-native-vector-icons/FontAwesome', () => 'Icon');
jest.mock('react-native-vector-icons/EvilIcons', () => 'Icon');
jest.mock('react-native-vector-icons/EvilIcons', () => 'Icon');

jest.mock('@react-native-camera-roll/camera-roll', () => ({
  getPhotos: jest.fn(),
  saveToCameraRoll: jest.fn(),
}));

jest.mock('rn-fetch-blob', () => ({
  DocumentDir: () => {},
  fs: {
    dirs: {
      CacheDir: '',
    },
  },
  config: () => ({
    fetch: () => ({
      path: () => {},
    }),
  }),
}));

jest.mock('react-native-share', () => ({
  open: jest.fn(),
  FACEBOOK: 'facebook',
}));


jest.mock('@bugsnag/react-native', () => {
  const Bugsnag = {
    start: jest.fn(),
    notify: jest.fn(),
    leaveBreadcrumb: jest.fn(),
    getPlugin: jest.fn(),
  };
  return Bugsnag;
});
jest.mock('react-native-permissions', () => {
  return {
    PERMISSIONS: {
      ANDROID: {
        READ_MEDIA_IMAGES: 'READ_MEDIA_IMAGES',
        READ_MEDIA_VIDEO: 'READ_MEDIA_VIDEO',
        READ_EXTERNAL_STORAGE: 'READ_EXTERNAL_STORAGE',
      },
    },
    RESULTS: {
      GRANTED: 'granted',
      DENIED: 'denied',
      BLOCKED: 'blocked',
    },
    requestMultiple: jest.fn(),
    request: jest.fn(),
  };
});

jest.mock('react-native-fs', () => {
  return {
    mkdir: jest.fn(),
    moveFile: jest.fn(),
    copyFile: jest.fn(),
    pathForBundle: jest.fn(),
    pathForGroup: jest.fn(),
    getFSInfo: jest.fn(),
    getAllExternalFilesDirs: jest.fn(),
    unlink: jest.fn(),
    exists: jest.fn(),
    stopDownload: jest.fn(),
    resumeDownload: jest.fn(),
    isResumable: jest.fn(),
    stopUpload: jest.fn(),
    completeHandlerIOS: jest.fn(),
    readDir: jest.fn(),
    readDirAssets: jest.fn(),
    existsAssets: jest.fn(),
    readdir: jest.fn(),
    setReadable: jest.fn(),
    stat: jest.fn(),
    readFile: jest.fn(),
    read: jest.fn(),
    readFileAssets: jest.fn(),
    hash: jest.fn(),
    copyFileAssets: jest.fn(),
    copyFileAssetsIOS: jest.fn(),
    copyAssetsVideoIOS: jest.fn(),
    writeFile: jest.fn(),
    appendFile: jest.fn(),
    write: jest.fn(),
    downloadFile: jest.fn(),
    uploadFiles: jest.fn(),
    touch: jest.fn(),
    MainBundlePath: jest.fn(),
    CachesDirectoryPath: jest.fn(),
    DocumentDirectoryPath: jest.fn(),
    ExternalDirectoryPath: jest.fn(),
    ExternalStorageDirectoryPath: jest.fn(),
    TemporaryDirectoryPath: jest.fn(),
    LibraryDirectoryPath: jest.fn(),
    PicturesDirectoryPath: jest.fn(),
  };
});

jest.mock('underscore', () => {
  return {
    ...jest.requireActual('underscore'),
    debounce: jest.fn(fn => fn),
    deep: jest.fn(),
  };
})

jest.mock('@react-navigation/native', () => ({
  CommonActions: {
    goBack: jest.fn(),
    setParams: jest.fn(),
  },
  useNavigation: () => ({
    dispatch: jest.fn(),
    navigate: jest.fn(),
  }),
}));

jest.mock('native-base', () => {
  const mockActionSheet = {
    show: jest
      .fn()
      .mockImplementation(
        ({options, cancelButtonIndex, destructiveButtonIndex}, callback) => {
          mockActionSheet.options = options;
          mockActionSheet.cancelButtonIndex = cancelButtonIndex;
          mockActionSheet.destructiveButtonIndex = destructiveButtonIndex;
          mockActionSheet.callback = callback;
        },
      ),
  };
  return {
    ...jest.requireActual('native-base'),
    ActionSheet: mockActionSheet,
  };
});
jest.mock('redux-persist', () => {
  return {
    ...jest.requireActual('redux-persist'),
    persistReducer: jest.fn().mockImplementation((config, reducers) => reducers),
    persistStore: jest.fn().mockImplementation(() => ({
      purge: jest.fn().mockResolvedValue(null),
    })),
  };
});
jest.mock('@react-navigation/native', () => {
  return {
    ...jest.requireActual('@react-navigation/native'),
    useNavigation: () => ({
      navigate: jest.fn(),
      dispatch: jest.fn(),
    }),
  };
});

jest.mock('@react-navigation/stack', () => ({
  createStackNavigator: jest.fn(() => ({
    Navigator: jest.fn(),
    Screen: jest.fn(),
  })),
}));

jest.mock('react-native/Libraries/Interaction/InteractionManager', () => ({
  runAfterInteractions: jest.fn(cb => cb()),
  createInteractionHandle: jest.fn(),
  clearInteractionHandle: jest.fn(),
  setDeadline: jest.fn(),
}));

jest.mock('react-native-pdf', () => ({
  PDFView: jest.fn(),
}));
jest.mock('react-native-neat-date-picker', () => ({
  DatePicker: jest.fn(),
}));
jest.mock('expo-notifications', () => ({
  setBadgeCountAsync: jest.fn(),
  removeNotificationSubscription: jest.fn(),
}));
jest.mock('expo-device', () => ({
  getDeviceTypeAsync: jest.fn(),
}));
jest.mock('expo-constants', () => ({
  Constants: {
    expoConfig: {
      extras:{
        eas:{
          projectId: '1234'
        }
      }
    }
  }
}));
jest.mock('react-native/Libraries/TurboModule/TurboModuleRegistry', () => {
  const turboModuleRegistry = jest.requireActual('react-native/Libraries/TurboModule/TurboModuleRegistry');
  return {
    ...turboModuleRegistry,
    getEnforcing: (name) => {
      // List of TurboModules libraries to mock.
      const modulesToMock = ['RNLocalize', 'SettingsManager'];
      if (modulesToMock.includes(name)) {
        return null;
      }
      return turboModuleRegistry.getEnforcing(name);
    },
  };
});
const listeners = [];
jest.mock('@react-native-community/netinfo', () => ({
  addEventListener: jest.fn((callback) => {
    listeners.push(callback);
    return () => {
      const index = listeners.indexOf(callback);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    };
  }),
  fetch: jest.fn().mockResolvedValue({ isConnected: true, type: 'wifi' }),
  __triggerConnectionChange: (state) => {
    listeners.forEach(listener => listener(state));
  }
}))
jest.mock("@fortawesome/react-native-fontawesome", () => ({
  FontAwesomeIcon: props => <mockIcon {...props}>icon</mockIcon>
}));
